nohup: ignoring input
2025-06-20 09:47:25,803 - __main__ - INFO - 🚀 启动 Telegram Bot 服务...
2025-06-20 09:47:25,804 - __main__ - INFO - 初始化 Telegram Bot...
2025-06-20 09:47:25,975 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/getMe "HTTP/1.1 200 OK"
2025-06-20 09:47:25,976 - __main__ - INFO - Telegram <PERSON> Application 初始化成功
2025-06-20 09:47:26,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/setMyCommands "HTTP/1.1 200 OK"
2025-06-20 09:47:26,152 - __main__ - INFO - Bot命令菜单设置成功
2025-06-20 09:47:26,152 - __main__ - INFO - 设置 Webhook...
2025-06-20 09:47:26,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/deleteWebhook "HTTP/1.1 200 OK"
2025-06-20 09:47:26,159 - __main__ - INFO - 旧的 Webhook 已删除。
2025-06-20 09:47:26,166 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/setWebhook "HTTP/1.1 200 OK"
2025-06-20 09:47:26,174 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/getWebhookInfo "HTTP/1.1 200 OK"
2025-06-20 09:47:26,174 - __main__ - INFO - Webhook 已成功设置到: https://hook.nyxn.ai/webhook
2025-06-20 09:47:26,175 - __main__ - INFO - Webhook 信息: WebhookInfo(has_custom_certificate=False, ip_address='*************', max_connections=40, pending_update_count=0, url='https://hook.nyxn.ai/webhook')
2025-06-20 09:47:26,175 - __main__ - INFO - ✅ Telegram Bot 初始化完成，准备启动服务器...
2025-06-20 09:47:26,177 - __main__ - INFO - 🌐 启动 Web 服务器，监听端口 5000
2025-06-20 09:47:26,180 - __main__ - INFO - 启动 Flask 服务器，监听 0.0.0.0:5000
 * Serving Flask app 'main'
 * Debug mode: off
2025-06-20 09:47:26,183 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-20 09:47:26,183 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-20 09:48:10,052 - werkzeug - INFO - 127.0.0.1 - - [20/Jun/2025 09:48:10] "GET /health HTTP/1.1" 200 -
2025-06-20 09:49:09,749 - werkzeug - INFO - 127.0.0.1 - - [20/Jun/2025 09:49:09] "GET /health HTTP/1.1" 200 -
2025-06-20 09:50:43,245 - werkzeug - INFO - 127.0.0.1 - - [20/Jun/2025 09:50:43] "GET /health HTTP/1.1" 200 -
2025-06-20 09:50:43,252 - werkzeug - INFO - 127.0.0.1 - - [20/Jun/2025 09:50:43] "GET /webhook/info HTTP/1.1" 200 -
2025-06-20 09:50:51,066 - werkzeug - INFO - 127.0.0.1 - - [20/Jun/2025 09:50:51] "POST /webhook HTTP/1.1" 200 -
