#!/bin/bash

# 新闻抓取守护进程停止脚本

echo "🛑 停止新闻抓取守护进程..."

# 停止screen会话
if screen -list | grep -q "news-crawler"; then
    echo "📱 终止screen会话..."
    screen -S news-crawler -X quit
    sleep 2
fi

# 强制停止进程
if pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    echo "🔄 停止守护进程..."
    pkill -f "python.*news_crawler_daemon.py"
    sleep 3
fi

# 检查是否成功停止
if pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    echo "⚠️  强制终止守护进程..."
    pkill -9 -f "python.*news_crawler_daemon.py"
    sleep 2
fi

# 验证停止状态
if ! pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    echo "✅ 新闻抓取守护进程已成功停止"
else
    echo "❌ 守护进程停止失败"
    exit 1
fi
