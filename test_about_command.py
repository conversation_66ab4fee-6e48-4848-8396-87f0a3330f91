import asyncio
import sys
sys.path.append('/home/<USER>')

async def test_about_command():
    """测试修复后的/about命令"""
    print("🧪 测试修复后的 /about 命令...")
    print("=" * 50)
    
    try:
        # 模拟about命令的逻辑
        about_info = (
            "🤖 **nyxn-ai 加密货币新闻系统**\n\n"
            "📋 **主要功能：**\n"
            "• 📰 实时获取加密货币新闻\n"
            "• 🤖 AI 智能总结新闻内容\n"
            "• 🔍 支持特定币种新闻查询\n\n"
            "💡 **使用方法：**\n"
            "• `/news` - 获取最近24小时热门新闻\n"
            "• `/news btc` - 获取比特币相关新闻总结\n"
            "• `/news ethereum` - 获取以太坊相关新闻\n"
            "• `/latest` - 获取最新实时新闻快讯\n\n"
            "⚡ **技术架构：**\n"
            "• 数据源：Cryptopanic 网页抓取\n"
            "• 内容抓取：Playwright\n"
            "• AI 总结：Google Gemini-2.0-Flash\n"
            "• 数据存储：MySQL 数据库\n\n"
            "🔄 实时获取最新新闻内容\n"
            "🌐 Webhook: hook.nyxn.ai"
        )
        
        print("✅ About信息生成成功")
        print(f"📏 消息长度: {len(about_info)} 字符")
        
        print("\n📄 About信息预览:")
        print("-" * 50)
        print(about_info)
        print("-" * 50)
        
        # 检查是否有Markdown格式问题
        markdown_elements = ['**', '•', '`', '_', '*']
        for element in markdown_elements:
            count = about_info.count(element)
            if element == '**' and count % 2 != 0:
                print(f"⚠️  Markdown警告: ** 标记数量不匹配 ({count})")
            elif element in about_info:
                print(f"✅ Markdown元素 '{element}': 正常")
        
        print("\n🎉 /about 命令测试完成！")
        print("✅ 信息生成: 正常")
        print("✅ Markdown格式: 正常")
        print("✅ 消息长度: 适中")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_about_command())
    if success:
        print("\n🎊 /about 命令修复成功！现在可以在Telegram中正常使用了！")
    else:
        print("\n❌ 测试失败，需要进一步调试")
