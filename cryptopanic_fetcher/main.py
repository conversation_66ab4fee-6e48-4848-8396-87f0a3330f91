import os
import requests
import time
from datetime import datetime
from dotenv import load_dotenv
from utils.db_utils import insert_news

# 加载环境变量
load_dotenv(override=True)

CRYPTOPANIC_API_KEY = os.getenv("CRYPTOPANIC_API_KEY")
CRYPTOPANIC_API_URL = "https://cryptopanic.com/api/developer/v2/posts/"

def fetch_cryptopanic_news():
    """
    从 Cryptopanic API 获取最新新闻。
    """
    if not CRYPTOPANIC_API_KEY:
        print("错误: CRYPTOPANIC_API_KEY 未设置。")
        return []

    params = {
        "auth_token": CRYPTOPANIC_API_KEY,
        "public": "true",
        "filter": "all" # 获取所有新闻
    }
    
    try:
        response = requests.get(CRYPTOPANIC_API_URL, params=params)
        response.raise_for_status() # 如果请求失败，抛出 HTTPError
        data = response.json()
        return data.get("posts", [])
    except requests.exceptions.RequestException as e:
        print(f"获取 Cryptopanic 新闻失败: {e}")
        return []

def process_and_store_news(news_items):
    """
    处理并存储新闻到数据库。
    """
    for item in news_items:
        # 提取所需字段
        title = item.get("title")
        published_at_str = item.get("published_at")
        source = item.get("source", {}).get("title")
        url = item.get("url")

        if not all([title, published_at_str, source, url]):
            print(f"跳过无效新闻项: {item}")
            continue

        # 转换 published_at 为 datetime 对象
        try:
            published_at = datetime.fromisoformat(published_at_str.replace('Z', '+00:00'))
        except ValueError:
            print(f"无效的日期格式: {published_at_str}, 跳过。")
            continue

        news_data = {
            "title": title,
            "published_at": published_at,
            "source": source,
            "url": url
        }
        insert_news(news_data)

def main():
    print("Cryptopanic 新闻获取服务启动...")
    print("⚠️  注意: 当前API配额已用完，服务将定期检查API状态")

    while True:
        print(f"[{datetime.now()}] 正在获取最新新闻...")
        news_items = fetch_cryptopanic_news()
        if news_items:
            print(f"成功获取到 {len(news_items)} 条新闻")
            process_and_store_news(news_items)
        else:
            print("未获取到新闻 - 可能是API配额限制或网络问题")

        print(f"等待60秒后重试...")
        time.sleep(60) # 每分钟运行一次

if __name__ == "__main__":
    main()
