#!/bin/bash
# 持续监控和修复命令菜单

echo "🔄 持续监控命令菜单..."

cd /home/<USER>
source venv/bin/activate

# 每30秒检查一次
while true; do
    echo "$(date): 检查命令菜单..."
    
    python3 -c "
import asyncio
import sys
sys.path.append('/home/<USER>')

async def check_and_fix():
    from telegram_bot.main import TELEGRAM_BOT_TOKEN
    from telegram import Bot, BotCommand, BotCommandScopeDefault
    
    bot = Bot(token=TELEGRAM_BOT_TOKEN)
    
    # 确保默认命令菜单正确
    commands = [
        BotCommand('news', '获取最近24小时加密货币新闻'),
        BotCommand('latest', '获取最新实时新闻快讯'),
        BotCommand('about', '了解系统功能和使用方法')
    ]
    
    await bot.set_my_commands(commands, scope=BotCommandScopeDefault())
    print('命令菜单已确认更新')

try:
    asyncio.run(check_and_fix())
except:
    pass
"
    
    sleep 30
done
