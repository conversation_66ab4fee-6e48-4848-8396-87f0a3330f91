{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud services and see the email address of your Google Account"}, "https://www.googleapis.com/auth/firebase": {"description": "View and administer all your Firebase data and settings"}, "https://www.googleapis.com/auth/firebase.readonly": {"description": "View all your Firebase data and settings"}}}}, "basePath": "", "baseUrl": "https://firebase.googleapis.com/", "batchPath": "batch", "canonicalName": "Firebase Management", "description": "The Firebase Management API enables programmatic setup and management of Firebase projects, including a project's Firebase resources and Firebase apps.", "discoveryVersion": "v1", "documentationLink": "https://firebase.google.com", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "firebase:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://firebase.mtls.googleapis.com/", "name": "firebase", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"availableProjects": {"methods": {"list": {"description": "Lists each [Google Cloud `Project`](https://cloud.google.com/resource-manager/reference/rest/v1/projects) that can have Firebase resources added and Firebase services enabled. A Project will only be listed if: - The caller has sufficient [Google IAM](https://cloud.google.com/iam) permissions to call AddFirebase. - The Project is not already a FirebaseProject. - The Project is not in an Organization which has policies that prevent Firebase resources from being added. ", "flatPath": "v1beta1/availableProjects", "httpMethod": "GET", "id": "firebase.availableProjects.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of Projects to return in the response. The server may return fewer than this value at its discretion. If no value is specified (or too large a value is specified), the server will impose its own limit. This value cannot be negative.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned from a previous call to `ListAvailableProjects` indicating where in the set of Projects to resume listing.", "location": "query", "type": "string"}}, "path": "v1beta1/availableProjects", "response": {"$ref": "ListAvailableProjectsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/operations/{operationsId}", "httpMethod": "GET", "id": "firebase.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^operations/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}, "projects": {"methods": {"addFirebase": {"description": "Adds Firebase resources and enables Firebase services in the specified existing [Google Cloud `Project`](https://cloud.google.com/resource-manager/reference/rest/v1/projects). Since a FirebaseProject is actually also a Google Cloud `Project`, a `FirebaseProject` has the same underlying Google Cloud identifiers (`projectNumber` and `projectId`). This allows for easy interop with Google APIs. The result of this call is an [`Operation`](../../v1beta1/operations). Poll the `Operation` to track the provisioning process by calling GetOperation until [`done`](../../v1beta1/operations#Operation.FIELDS.done) is `true`. When `done` is `true`, the `Operation` has either succeeded or failed. If the `Operation` succeeded, its [`response`](../../v1beta1/operations#Operation.FIELDS.response) is set to a FirebaseProject; if the `Operation` failed, its [`error`](../../v1beta1/operations#Operation.FIELDS.error) is set to a google.rpc.Status. The `Operation` is automatically deleted after completion, so there is no need to call DeleteOperation. This method does not modify any billing account information on the underlying Google Cloud `Project`. To call `AddFirebase`, a project member or service account must have the following permissions (the IAM roles of Editor and Owner contain these permissions): `firebase.projects.update`, `resourcemanager.projects.get`, `serviceusage.services.enable`, and `serviceusage.services.get`.", "flatPath": "v1beta1/projects/{projectsId}:addFirebase", "httpMethod": "POST", "id": "firebase.projects.addFirebase", "parameterOrder": ["project"], "parameters": {"project": {"description": "The resource name of the Google Cloud `Project` in which Firebase resources will be added and Firebase services enabled, in the format: projects/ PROJECT_IDENTIFIER Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values. After calling `AddFirebase`, the unique Project identifiers ( [`projectNumber`](https://cloud.google.com/resource-manager/reference/rest/v1/projects#Project.FIELDS.project_number) and [`projectId`](https://cloud.google.com/resource-manager/reference/rest/v1/projects#Project.FIELDS.project_id)) of the underlying Google Cloud `Project` are also the identifiers of the FirebaseProject.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+project}:addFirebase", "request": {"$ref": "AddFirebaseRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "addGoogleAnalytics": {"description": "Links the specified FirebaseProject with an existing [Google Analytics account](http://www.google.com/analytics/). Using this call, you can either: - Specify an `analyticsAccountId` to provision a new Google Analytics property within the specified account and associate the new property with the `FirebaseProject`. - Specify an existing `analyticsPropertyId` to associate the property with the `FirebaseProject`. Note that when you call `AddGoogleAnalytics`: 1. The first check determines if any existing data streams in the Google Analytics property correspond to any existing Firebase Apps in the `FirebaseProject` (based on the `packageName` or `bundleId` associated with the data stream). Then, as applicable, the data streams and apps are linked. Note that this auto-linking only applies to `AndroidApps` and `IosApps`. 2. If no corresponding data streams are found for the Firebase Apps, new data streams are provisioned in the Google Analytics property for each of the Firebase Apps. Note that a new data stream is always provisioned for a Web App even if it was previously associated with a data stream in the Analytics property. Learn more about the hierarchy and structure of Google Analytics accounts in the [Analytics documentation](https://support.google.com/analytics/answer/9303323). The result of this call is an [`Operation`](../../v1beta1/operations). Poll the `Operation` to track the provisioning process by calling GetOperation until [`done`](../../v1beta1/operations#Operation.FIELDS.done) is `true`. When `done` is `true`, the `Operation` has either succeeded or failed. If the `Operation` succeeded, its [`response`](../../v1beta1/operations#Operation.FIELDS.response) is set to an AnalyticsDetails; if the `Operation` failed, its [`error`](../../v1beta1/operations#Operation.FIELDS.error) is set to a google.rpc.Status. To call `AddGoogleAnalytics`, a project member must be an Owner for the existing `FirebaseProject` and have the [`Edit` permission](https://support.google.com/analytics/answer/2884495) for the Google Analytics account. If the `FirebaseProject` already has Google Analytics enabled, and you call `AddGoogleAnalytics` using an `analyticsPropertyId` that's different from the currently associated property, then the call will fail. Analytics may have already been enabled in the Firebase console or by specifying `timeZone` and `regionCode` in the call to [`AddFirebase`](../../v1beta1/projects/addFirebase).", "flatPath": "v1beta1/projects/{projectsId}:addGoogleAnalytics", "httpMethod": "POST", "id": "firebase.projects.addGoogleAnalytics", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The resource name of the FirebaseProject to link to an existing Google Analytics account, in the format: projects/PROJECT_IDENTIFIER Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}:addGoogleAnalytics", "request": {"$ref": "AddGoogleAnalyticsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified FirebaseProject.", "flatPath": "v1beta1/projects/{projectsId}", "httpMethod": "GET", "id": "firebase.projects.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the FirebaseProject, in the format: projects/ PROJECT_IDENTIFIER Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "FirebaseProject"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "getAdminSdkConfig": {"description": "Gets the configuration artifact associated with the specified FirebaseProject, which can be used by servers to simplify initialization. Typically, this configuration is used with the Firebase Admin SDK [initializeApp](https://firebase.google.com/docs/admin/setup#initialize_the_sdk) command.", "flatPath": "v1beta1/projects/{projectsId}/adminSdkConfig", "httpMethod": "GET", "id": "firebase.projects.getAdminSdkConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the FirebaseProject, in the format: projects/ PROJECT_IDENTIFIER/adminSdkConfig Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+/adminSdkConfig$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "AdminSdkConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "getAnalyticsDetails": {"description": "Gets the Google Analytics details currently associated with the specified FirebaseProject. If the `FirebaseProject` is not yet linked to Google Analytics, then the response to `GetAnalyticsDetails` is `NOT_FOUND`.", "flatPath": "v1beta1/projects/{projectsId}/analyticsDetails", "httpMethod": "GET", "id": "firebase.projects.getAnalyticsDetails", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the FirebaseProject, in the format: projects/ PROJECT_IDENTIFIER/analyticsDetails Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+/analyticsDetails$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "AnalyticsDetails"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists each FirebaseProject accessible to the caller. The elements are returned in no particular order, but they will be a consistent view of the Projects when additional requests are made with a `pageToken`. This method is eventually consistent with Project mutations, which means newly provisioned Projects and recent modifications to existing Projects might not be reflected in the set of Projects. The list will include only ACTIVE Projects. Use GetFirebaseProject for consistent reads as well as for additional Project details.", "flatPath": "v1beta1/projects", "httpMethod": "GET", "id": "firebase.projects.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of Projects to return in the response. The server may return fewer than this at its discretion. If no value is specified (or too large a value is specified), the server will impose its own limit. This value cannot be negative.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned from a previous call to `ListFirebaseProjects` indicating where in the set of Projects to resume listing.", "location": "query", "type": "string"}, "showDeleted": {"description": "Optional. Controls whether Projects in the DELETED state should be returned in the response. If not specified, only `ACTIVE` Projects will be returned.", "location": "query", "type": "boolean"}}, "path": "v1beta1/projects", "response": {"$ref": "ListFirebaseProjectsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": "Updates the attributes of the specified FirebaseProject. All [query parameters](#query-parameters) are required.", "flatPath": "v1beta1/projects/{projectsId}", "httpMethod": "PATCH", "id": "firebase.projects.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the Project, in the format: projects/PROJECT_IDENTIFIER PROJECT_IDENTIFIER: the Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Specifies which fields of the FirebaseProject to update. Note that the following fields are immutable: `name`, `project_id`, and `project_number`. To update `state`, use any of the following Google Cloud endpoints: [`projects.delete`](https://cloud.google.com/resource-manager/reference/rest/v1/projects/delete) or [`projects.undelete`](https://cloud.google.com/resource-manager/reference/rest/v1/projects/undelete)", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "FirebaseProject"}, "response": {"$ref": "FirebaseProject"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "removeAnalytics": {"description": "Unlinks the specified FirebaseProject from its Google Analytics account. This call removes the association of the specified `FirebaseProject` with its current Google Analytics property. However, this call does not delete the Google Analytics resources, such as the Google Analytics property or any data streams. These resources may be re-associated later to the `FirebaseProject` by calling [`AddGoogleAnalytics`](../../v1beta1/projects/addGoogleAnalytics) and specifying the same `analyticsPropertyId`. For Android Apps and iOS Apps, this call re-links data streams with their corresponding apps. However, for Web Apps, this call provisions a *new* data stream for each Web App. To call `RemoveAnalytics`, a project member must be an Owner for the `FirebaseProject`.", "flatPath": "v1beta1/projects/{projectsId}:removeAnalytics", "httpMethod": "POST", "id": "firebase.projects.removeAnalytics", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The resource name of the FirebaseProject to unlink from its Google Analytics account, in the format: projects/PROJECT_IDENTIFIER Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}:removeAnalytics", "request": {"$ref": "RemoveAnalyticsRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "searchApps": {"description": "Lists all available Apps for the specified FirebaseProject. This is a convenience method. Typically, interaction with an App should be done using the platform-specific service, but some tool use-cases require a summary of all known Apps (such as for App selector interfaces).", "flatPath": "v1beta1/projects/{projectsId}:searchApps", "httpMethod": "GET", "id": "firebase.projects.searchApps", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A query string compatible with Google's [AIP-160 standard](https://google.aip.dev/160). Use any of the following fields in a query: * [`app_id`](../projects/searchApps#FirebaseAppInfo.FIELDS.app_id) * [`namespace`](../projects/searchApps#FirebaseAppInfo.FIELDS.namespace) * [`platform`](../projects/searchApps#FirebaseAppInfo.FIELDS.platform) This query also supports the following \"virtual\" fields. These are fields which are not actually part of the returned resource object, but they can be queried as if they are pre-populated with specific values. * `sha1_hash` or `sha1_hashes`: This field is considered to be a _repeated_ `string` field, populated with the list of all SHA-1 certificate fingerprints registered with the AndroidApp. This list is empty if the App is not an `AndroidApp`. * `sha256_hash` or `sha256_hashes`: This field is considered to be a _repeated_ `string` field, populated with the list of all SHA-256 certificate fingerprints registered with the AndroidApp. This list is empty if the App is not an `AndroidApp`. * `app_store_id`: This field is considered to be a _singular_ `string` field, populated with the Apple App Store ID registered with the IosApp. This field is empty if the App is not an `IosApp`. * `team_id`: This field is considered to be a _singular_ `string` field, populated with the Apple team ID registered with the IosApp. This field is empty if the App is not an `IosApp`.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of Apps to return in the response. The server may return fewer than this value at its discretion. If no value is specified (or too large a value is specified), then the server will impose its own limit. This value cannot be negative.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned from a previous call to `SearchFirebaseApps` indicating where in the set of Apps to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "The parent FirebaseProject for which to list Apps, in the format: projects/ PROJECT_IDENTIFIER Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Controls whether Apps in the DELETED state should be returned. If not specified, only `ACTIVE` Apps will be returned.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}:searchApps", "response": {"$ref": "SearchFirebaseAppsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}, "resources": {"androidApps": {"methods": {"create": {"description": "Requests the creation of a new AndroidApp in the specified FirebaseProject. The result of this call is an `Operation` which can be used to track the provisioning process. The `Operation` is automatically deleted after completion, so there is no need to call `DeleteOperation`.", "flatPath": "v1beta1/projects/{projectsId}/androidApps", "httpMethod": "POST", "id": "firebase.projects.androidApps.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The resource name of the parent FirebaseProject in which to create an AndroidApp, in the format: projects/PROJECT_IDENTIFIER/androidApps Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/androidApps", "request": {"$ref": "AndroidApp"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified AndroidApp.", "flatPath": "v1beta1/projects/{projectsId}/androidApps/{androidAppsId}", "httpMethod": "GET", "id": "firebase.projects.androidApps.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the AndroidApp, in the format: projects/ PROJECT_IDENTIFIER/androidApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/androidApps/APP_ID Refer to the `AndroidApp` [`name`](../projects.androidApps#AndroidApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/androidApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "AndroidApp"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "getConfig": {"description": "Gets the configuration artifact associated with the specified AndroidApp.", "flatPath": "v1beta1/projects/{projectsId}/androidApps/{androidAppsId}/config", "httpMethod": "GET", "id": "firebase.projects.androidApps.getConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the AndroidApp configuration to download, in the format: projects/PROJECT_IDENTIFIER/androidApps/APP_ID/config Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/androidApps/APP_ID Refer to the `AndroidApp` [`name`](../projects.androidApps#AndroidApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/androidApps/[^/]+/config$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "AndroidAppConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists each AndroidApp associated with the specified FirebaseProject. The elements are returned in no particular order, but will be a consistent view of the Apps when additional requests are made with a `pageToken`.", "flatPath": "v1beta1/projects/{projectsId}/androidApps", "httpMethod": "GET", "id": "firebase.projects.androidApps.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of Apps to return in the response. The server may return fewer than this at its discretion. If no value is specified (or too large a value is specified), then the server will impose its own limit.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned from a previous call to `ListAndroidApps` indicating where in the set of Apps to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "The resource name of the parent FirebaseProject for which to list each associated AndroidApp, in the format: projects/PROJECT_IDENTIFIER /androidApps Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Controls whether Apps in the DELETED state should be returned in the response. If not specified, only `ACTIVE` Apps will be returned.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/androidApps", "response": {"$ref": "ListAndroidAppsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": "Updates the attributes of the specified AndroidApp.", "flatPath": "v1beta1/projects/{projectsId}/androidApps/{androidAppsId}", "httpMethod": "PATCH", "id": "firebase.projects.androidApps.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the AndroidApp, in the format: projects/ PROJECT_IDENTIFIER/androidApps/APP_ID * PROJECT_IDENTIFIER: the parent Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`. * APP_ID: the globally unique, Firebase-assigned identifier for the App (see [`appId`](../projects.androidApps#AndroidApp.FIELDS.app_id)).", "location": "path", "pattern": "^projects/[^/]+/androidApps/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Specifies which fields of the AndroidApp to update. Note that the following fields are immutable: `name`, `app_id`, `project_id`, and `package_name`. To update `state`, use any of the following endpoints: RemoveAndroidApp or UndeleteAndroidApp.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "AndroidApp"}, "response": {"$ref": "AndroidApp"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "remove": {"description": "Removes the specified AndroidApp from the FirebaseProject.", "flatPath": "v1beta1/projects/{projectsId}/androidApps/{androidAppsId}:remove", "httpMethod": "POST", "id": "firebase.projects.androidApps.remove", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the AndroidApp, in the format: projects/ PROJECT_IDENTIFIER/androidApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/androidApps/APP_ID Refer to the AndroidApp [name](../projects.androidApps#AndroidApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/androidApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:remove", "request": {"$ref": "RemoveAndroidAppRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "undelete": {"description": "Restores the specified AndroidApp to the FirebaseProject.", "flatPath": "v1beta1/projects/{projectsId}/androidApps/{androidAppsId}:undelete", "httpMethod": "POST", "id": "firebase.projects.androidApps.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the AndroidApp, in the format: projects/ PROJECT_IDENTIFIER/androidApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/androidApps/APP_ID Refer to the AndroidApp [name](../projects.androidApps#AndroidApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/androidApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:undelete", "request": {"$ref": "UndeleteAndroidAppRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}, "resources": {"sha": {"methods": {"create": {"description": "Adds a ShaCertificate to the specified AndroidApp.", "flatPath": "v1beta1/projects/{projectsId}/androidApps/{androidAppsId}/sha", "httpMethod": "POST", "id": "firebase.projects.androidApps.sha.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The resource name of the parent AndroidApp to which to add a ShaCertificate, in the format: projects/PROJECT_IDENTIFIER/androidApps/ APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/androidApps/APP_ID Refer to the `AndroidApp` [`name`](../projects.androidApps#AndroidApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/androidApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/sha", "request": {"$ref": "ShaCertificate"}, "response": {"$ref": "ShaCertificate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Removes a ShaCertificate from the specified AndroidApp.", "flatPath": "v1beta1/projects/{projectsId}/androidApps/{androidAppsId}/sha/{shaId}", "httpMethod": "DELETE", "id": "firebase.projects.androidApps.sha.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the ShaCertificate to remove from the parent AndroidApp, in the format: projects/PROJECT_IDENTIFIER/androidApps/APP_ID /sha/SHA_HASH Refer to the `ShaCertificate` [`name`](../projects.androidApps.sha#ShaCertificate.FIELDS.name) field for details about PROJECT_IDENTIFIER, APP_ID, and SHA_HASH values. You can obtain the full resource name of the `ShaCertificate` from the response of [`ListShaCertificates`](../projects.androidApps.sha/list) or the original [`CreateShaCertificate`](../projects.androidApps.sha/create).", "location": "path", "pattern": "^projects/[^/]+/androidApps/[^/]+/sha/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "list": {"description": "Lists the SHA-1 and SHA-256 certificates for the specified AndroidApp.", "flatPath": "v1beta1/projects/{projectsId}/androidApps/{androidAppsId}/sha", "httpMethod": "GET", "id": "firebase.projects.androidApps.sha.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The resource name of the parent AndroidApp for which to list each associated ShaCertificate, in the format: projects/PROJECT_IDENTIFIER /androidApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/androidApps/APP_ID Refer to the `AndroidApp` [`name`](../projects.androidApps#AndroidApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/androidApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/sha", "response": {"$ref": "ListShaCertificatesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}}}, "availableLocations": {"deprecated": true, "methods": {"list": {"deprecated": true, "description": "**DECOMMISSIONED.** **If called, this endpoint will return a 404 error.** _Instead, use the applicable resource-specific REST API (or associated documentation, as needed) to determine valid locations for each resource used in your Project._ Lists the valid [\"locations for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location) for the specified Project (including a FirebaseProject). One of these locations can be selected as the Project's location for default Google Cloud resources, which is the geographical location where the Project's resources associated with Google App Engine (such as the default Cloud Firestore instance) will be provisioned by default. However, if the location for default Google Cloud resources has already been set for the Project, then this setting cannot be changed. This call checks for any possible [location restrictions](https://cloud.google.com/resource-manager/docs/organization-policy/defining-locations) for the specified Project and, thus, might return a subset of all possible locations. To list all locations (regardless of any restrictions), call the endpoint without specifying a unique project identifier (that is, `/v1beta1/{parent=projects/-}/listAvailableLocations`). To call `ListAvailableLocations` with a specified project, a member must be at minimum a Viewer of the Project. Calls without a specified project do not require any specific project permissions.", "flatPath": "v1beta1/projects/{projectsId}/availableLocations", "httpMethod": "GET", "id": "firebase.projects.availableLocations.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of locations to return in the response. The server may return fewer than this value at its discretion. If no value is specified (or too large a value is specified), then the server will impose its own limit. This value cannot be negative.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned from a previous call to `ListAvailableLocations` indicating where in the list of locations to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "The FirebaseProject for which to list [locations for default Google Cloud resources](https://firebase.google.com/docs/projects/locations#default-cloud-location), in the format: projects/PROJECT_IDENTIFIER Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values. If no unique project identifier is specified (that is, `projects/-`), the returned list does not take into account org-specific or project-specific location restrictions.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/availableLocations", "response": {"$ref": "ListAvailableLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}, "defaultLocation": {"deprecated": true, "methods": {"finalize": {"deprecated": true, "description": "**DECOMMISSIONED.** **If called, this endpoint will return a 404 error.** _Instead, use the applicable resource-specific REST API to set the location for each resource used in your Project._ Sets the [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location) for the specified FirebaseProject. This method creates a Google App Engine application with a [default Cloud Storage bucket](https://cloud.google.com/appengine/docs/standard/python/googlecloudstorageclient/setting-up-cloud-storage#activating_a_cloud_storage_bucket), located in the specified [`locationId`](#body.request_body.FIELDS.location_id). This location must be one of the available [App Engine locations](https://cloud.google.com/about/locations#region). After the location for default Google Cloud resources is finalized, or if it was already set, it cannot be changed. The location for default Google Cloud resources for the specified `FirebaseProject` might already be set because either the underlying Google Cloud `Project` already has an App Engine application or `FinalizeDefaultLocation` was previously called with a specified `locationId`. The result of this call is an [`Operation`](../../v1beta1/operations), which can be used to track the provisioning process. The [`response`](../../v1beta1/operations#Operation.FIELDS.response) type of the `Operation` is google.protobuf.Empty. The `Operation` can be polled by its `name` using GetOperation until `done` is true. When `done` is true, the `Operation` has either succeeded or failed. If the `Operation` has succeeded, its [`response`](../../v1beta1/operations#Operation.FIELDS.response) will be set to a google.protobuf.Empty; if the `Operation` has failed, its `error` will be set to a google.rpc.Status. The `Operation` is automatically deleted after completion, so there is no need to call DeleteOperation. All fields listed in the [request body](#request-body) are required. To call `FinalizeDefaultLocation`, a member must be an Owner of the Project.", "flatPath": "v1beta1/projects/{projectsId}/defaultLocation:finalize", "httpMethod": "POST", "id": "firebase.projects.defaultLocation.finalize", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The resource name of the FirebaseProject for which the [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location) will be set, in the format: projects/PROJECT_IDENTIFIER Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/defaultLocation:finalize", "request": {"$ref": "FinalizeDefaultLocationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}}, "iosApps": {"methods": {"create": {"description": "Requests the creation of a new IosApp in the specified FirebaseProject. The result of this call is an `Operation` which can be used to track the provisioning process. The `Operation` is automatically deleted after completion, so there is no need to call `DeleteOperation`.", "flatPath": "v1beta1/projects/{projectsId}/iosApps", "httpMethod": "POST", "id": "firebase.projects.iosApps.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The resource name of the parent FirebaseProject in which to create an IosApp, in the format: projects/PROJECT_IDENTIFIER/iosApps Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/iosApps", "request": {"$ref": "IosApp"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified IosApp.", "flatPath": "v1beta1/projects/{projectsId}/iosApps/{iosAppsId}", "httpMethod": "GET", "id": "firebase.projects.iosApps.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the IosApp, in the format: projects/PROJECT_IDENTIFIER /iosApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/iosApps/APP_ID Refer to the `IosApp` [`name`](../projects.iosApps#IosApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/iosApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "IosApp"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "getConfig": {"description": "Gets the configuration artifact associated with the specified IosApp.", "flatPath": "v1beta1/projects/{projectsId}/iosApps/{iosAppsId}/config", "httpMethod": "GET", "id": "firebase.projects.iosApps.getConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the App configuration to download, in the format: projects/PROJECT_IDENTIFIER/iosApps/APP_ID/config Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/iosApps/APP_ID Refer to the `IosApp` [`name`](../projects.iosApps#IosApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/iosApps/[^/]+/config$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "IosAppConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists each IosApp associated with the specified FirebaseProject. The elements are returned in no particular order, but will be a consistent view of the Apps when additional requests are made with a `pageToken`.", "flatPath": "v1beta1/projects/{projectsId}/iosApps", "httpMethod": "GET", "id": "firebase.projects.iosApps.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of Apps to return in the response. The server may return fewer than this at its discretion. If no value is specified (or too large a value is specified), the server will impose its own limit.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned from a previous call to `ListIosApps` indicating where in the set of Apps to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "The resource name of the parent FirebaseProject for which to list each associated IosApp, in the format: projects/PROJECT_IDENTIFIER/iosApps Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Controls whether Apps in the DELETED state should be returned in the response. If not specified, only `ACTIVE` Apps will be returned.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/iosApps", "response": {"$ref": "ListIosAppsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": "Updates the attributes of the specified IosApp.", "flatPath": "v1beta1/projects/{projectsId}/iosApps/{iosAppsId}", "httpMethod": "PATCH", "id": "firebase.projects.iosApps.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the IosApp, in the format: projects/PROJECT_IDENTIFIER /iosApps/APP_ID * PROJECT_IDENTIFIER: the parent Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`. * APP_ID: the globally unique, Firebase-assigned identifier for the App (see [`appId`](../projects.iosApps#IosApp.FIELDS.app_id)).", "location": "path", "pattern": "^projects/[^/]+/iosApps/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Specifies which fields of the IosApp to update. Note that the following fields are immutable: `name`, `app_id`, `project_id`, and `bundle_id`. To update `state`, use any of the following endpoints: RemoveIosApp or UndeleteIosApp.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "IosApp"}, "response": {"$ref": "IosApp"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "remove": {"description": "Removes the specified IosApp from the FirebaseProject.", "flatPath": "v1beta1/projects/{projectsId}/iosApps/{iosAppsId}:remove", "httpMethod": "POST", "id": "firebase.projects.iosApps.remove", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the IosApp, in the format: projects/ PROJECT_IDENTIFIER/iosApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/iosApps/APP_ID Refer to the IosApp [name](../projects.iosApps#IosApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/iosApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:remove", "request": {"$ref": "RemoveIosAppRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "undelete": {"description": "Restores the specified IosApp to the FirebaseProject.", "flatPath": "v1beta1/projects/{projectsId}/iosApps/{iosAppsId}:undelete", "httpMethod": "POST", "id": "firebase.projects.iosApps.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the IosApp, in the format: projects/ PROJECT_IDENTIFIER/iosApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/iosApps/APP_ID Refer to the IosApp [name](../projects.iosApps#IosApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/iosApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:undelete", "request": {"$ref": "UndeleteIosAppRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}}, "webApps": {"methods": {"create": {"description": "Requests the creation of a new WebApp in the specified FirebaseProject. The result of this call is an `Operation` which can be used to track the provisioning process. The `Operation` is automatically deleted after completion, so there is no need to call `DeleteOperation`.", "flatPath": "v1beta1/projects/{projectsId}/webApps", "httpMethod": "POST", "id": "firebase.projects.webApps.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The resource name of the parent FirebaseProject in which to create a WebApp, in the format: projects/PROJECT_IDENTIFIER/webApps Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/webApps", "request": {"$ref": "WebApp"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified WebApp.", "flatPath": "v1beta1/projects/{projectsId}/webApps/{webAppsId}", "httpMethod": "GET", "id": "firebase.projects.webApps.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the WebApp, in the format: projects/PROJECT_IDENTIFIER /webApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/webApps/APP_ID Refer to the `WebApp` [`name`](../projects.webApps#WebApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/webApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "WebApp"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "getConfig": {"description": "Gets the configuration artifact associated with the specified WebApp.", "flatPath": "v1beta1/projects/{projectsId}/webApps/{webAppsId}/config", "httpMethod": "GET", "id": "firebase.projects.webApps.getConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the WebApp configuration to download, in the format: projects/PROJECT_IDENTIFIER/webApps/APP_ID/config Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/webApps/APP_ID Refer to the `WebApp` [`name`](../projects.webApps#WebApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/webApps/[^/]+/config$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "WebAppConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists each WebApp associated with the specified FirebaseProject. The elements are returned in no particular order, but will be a consistent view of the Apps when additional requests are made with a `pageToken`.", "flatPath": "v1beta1/projects/{projectsId}/webApps", "httpMethod": "GET", "id": "firebase.projects.webApps.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of Apps to return in the response. The server may return fewer than this value at its discretion. If no value is specified (or too large a value is specified), then the server will impose its own limit.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned from a previous call to `ListWebApps` indicating where in the set of Apps to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "The resource name of the parent FirebaseProject for which to list each associated WebApp, in the format: projects/PROJECT_IDENTIFIER/webApps Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Controls whether Apps in the DELETED state should be returned in the response. If not specified, only `ACTIVE` Apps will be returned.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/webApps", "response": {"$ref": "ListWebAppsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": "Updates the attributes of the specified WebApp.", "flatPath": "v1beta1/projects/{projectsId}/webApps/{webAppsId}", "httpMethod": "PATCH", "id": "firebase.projects.webApps.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the WebApp, in the format: projects/PROJECT_IDENTIFIER /webApps/APP_ID * PROJECT_IDENTIFIER: the parent Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`. * APP_ID: the globally unique, Firebase-assigned identifier for the App (see [`appId`](../projects.webApps#WebApp.FIELDS.app_id)).", "location": "path", "pattern": "^projects/[^/]+/webApps/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Specifies which fields of the WebApp to update. Note that the following fields are immutable: `name`, `app_id`, and `project_id`. To update `state`, use any of the following endpoints: RemoveWebApp or UndeleteWebApp.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "WebApp"}, "response": {"$ref": "WebApp"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "remove": {"description": "Removes the specified WebApp from the FirebaseProject.", "flatPath": "v1beta1/projects/{projectsId}/webApps/{webAppsId}:remove", "httpMethod": "POST", "id": "firebase.projects.webApps.remove", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the WebApp, in the format: projects/ PROJECT_IDENTIFIER/webApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/webApps/APP_ID Refer to the WebApp [name](../projects.webApps#WebApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/webApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:remove", "request": {"$ref": "RemoveWebAppRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "undelete": {"description": "Restores the specified WebApp to the FirebaseProject.", "flatPath": "v1beta1/projects/{projectsId}/webApps/{webAppsId}:undelete", "httpMethod": "POST", "id": "firebase.projects.webApps.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the WebApp, in the format: projects/ PROJECT_IDENTIFIER/webApps/APP_ID Since an APP_ID is a unique identifier, the Unique Resource from Sub-Collection access pattern may be used here, in the format: projects/-/webApps/APP_ID Refer to the WebApp [name](../projects.webApps#WebApp.FIELDS.name) field for details about PROJECT_IDENTIFIER and APP_ID values.", "location": "path", "pattern": "^projects/[^/]+/webApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:undelete", "request": {"$ref": "UndeleteWebAppRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}}}}}, "revision": "20250331", "rootUrl": "https://firebase.googleapis.com/", "schemas": {"AddFirebaseRequest": {"description": "All fields are required.", "id": "AddFirebaseRequest", "properties": {"locationId": {"description": "**DEPRECATED.** _Instead, use product-specific REST APIs to work with the location of each resource in a Project. This field may be ignored, especially for newly provisioned projects after October 30, 2024._ The ID of the Project's [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location), which are resources associated with Google App Engine. The location must be one of the available [Google App Engine locations](https://cloud.google.com/about/locations#region).", "type": "string"}}, "type": "object"}, "AddGoogleAnalyticsRequest": {"id": "AddGoogleAnalyticsRequest", "properties": {"analyticsAccountId": {"description": "The ID for the existing [Google Analytics account](http://www.google.com/analytics/) that you want to link with the `FirebaseProject`. Specifying this field will provision a new Google Analytics property in your Google Analytics account and associate the new property with the `FirebaseProject`.", "type": "string"}, "analyticsPropertyId": {"description": "The ID for the existing Google Analytics property that you want to associate with the `FirebaseProject`.", "type": "string"}}, "type": "object"}, "AdminSdkConfig": {"id": "AdminSdkConfig", "properties": {"databaseURL": {"deprecated": true, "description": "**DEPRECATED.** _Instead, find the URL of the default Realtime Database instance using the [list endpoint](https://firebase.google.com/docs/reference/rest/database/database-management/rest/v1beta/projects.locations.instances/list) within the Firebase Realtime Database REST API. If the default instance for the Project has not yet been provisioned, the return might not contain a default instance. Note that the config that's generated for the Firebase console or the Firebase CLI uses the Realtime Database endpoint to populate this value for that config._ The URL of the default Firebase Realtime Database instance.", "type": "string"}, "locationId": {"deprecated": true, "description": "**DEPRECATED.** _Instead, use product-specific REST APIs to find the location of each resource in a Project. This field may not be populated, especially for newly provisioned projects after October 30, 2024._ The ID of the Project's [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location), which are resources associated with Google App Engine. The location is one of the available [App Engine locations](https://cloud.google.com/about/locations#region). This field is omitted if the location for default Google Cloud resources has not been set.", "type": "string"}, "projectId": {"description": "Immutable. A user-assigned unique identifier for the `FirebaseProject`. This identifier may appear in URLs or names for some Firebase resources associated with the Project, but it should generally be treated as a convenience alias to reference the Project.", "type": "string"}, "storageBucket": {"deprecated": true, "description": "**DEPRECATED.** _Instead, find the name of the default Cloud Storage for Firebase bucket using the [list endpoint](https://firebase.google.com/docs/reference/rest/storage/rest/v1beta/projects.buckets/list) within the Cloud Storage for Firebase REST API. If the default bucket for the Project has not yet been provisioned, the return might not contain a default bucket. Note that the config that's generated for the Firebase console or the Firebase CLI uses the Cloud Storage for Firebase endpoint to populate this value for that config._ The name of the default Cloud Storage for Firebase bucket.", "type": "string"}}, "type": "object"}, "AnalyticsDetails": {"id": "AnalyticsDetails", "properties": {"analyticsProperty": {"$ref": "AnalyticsProperty", "description": "The Analytics Property object associated with the specified `FirebaseProject`. This object contains the details of the Google Analytics property associated with the Project."}, "streamMappings": {"description": " - For `AndroidApps` and `IosApps`: a map of `app` to `streamId` for each Firebase App in the specified `FirebaseProject`. Each `app` and `streamId` appears only once. - For `WebApps`: a map of `app` to `streamId` and `measurementId` for each `WebApp` in the specified `FirebaseProject`. Each `app`, `streamId`, and `measurementId` appears only once.", "items": {"$ref": "StreamMapping"}, "type": "array"}}, "type": "object"}, "AnalyticsProperty": {"description": "Details of a Google Analytics property", "id": "AnalyticsProperty", "properties": {"analyticsAccountId": {"description": "Output only. The ID of the [Google Analytics account](https://www.google.com/analytics/) for the Google Analytics property associated with the specified FirebaseProject.", "readOnly": true, "type": "string"}, "displayName": {"description": "The display name of the Google Analytics property associated with the specified `FirebaseProject`.", "type": "string"}, "id": {"description": "The globally unique, Google-assigned identifier of the Google Analytics property associated with the specified `FirebaseProject`. If you called [`AddGoogleAnalytics`](../../v1beta1/projects/addGoogleAnalytics) to link the `FirebaseProject` with a Google Analytics account, the value in this `id` field is the same as the ID of the property either specified or provisioned with that call to `AddGoogleAnalytics`.", "type": "string"}}, "type": "object"}, "AndroidApp": {"description": "Details of a Firebase App for Android.", "id": "AndroidApp", "properties": {"apiKeyId": {"description": "The globally unique, Google-assigned identifier (UID) for the Firebase API key associated with the `AndroidApp`. Be aware that this value is the UID of the API key, _not_ the [`keyString`](https://cloud.google.com/api-keys/docs/reference/rest/v2/projects.locations.keys#Key.FIELDS.key_string) of the API key. The `keyString` is the value that can be found in the App's [configuration artifact](../../rest/v1beta1/projects.androidApps/getConfig). If `api_key_id` is not set in requests to [`androidApps.Create`](../../rest/v1beta1/projects.androidApps/create), then Firebase automatically associates an `api_key_id` with the `AndroidApp`. This auto-associated key may be an existing valid key or, if no valid key exists, a new one will be provisioned. In patch requests, `api_key_id` cannot be set to an empty value, and the new UID must have no restrictions or only have restrictions that are valid for the associated `AndroidApp`. We recommend using the [Google Cloud Console](https://console.cloud.google.com/apis/credentials) to manage API keys.", "type": "string"}, "appId": {"description": "Output only. Immutable. The globally unique, Firebase-assigned identifier for the `AndroidApp`. This identifier should be treated as an opaque token, as the data format is not specified.", "readOnly": true, "type": "string"}, "displayName": {"description": "The user-assigned display name for the `AndroidApp`.", "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and it may be sent with update requests to ensure the client has an up-to-date value before proceeding. Learn more about `etag` in Google's [AIP-154 standard](https://google.aip.dev/154#declarative-friendly-resources). This etag is strongly validated.", "type": "string"}, "expireTime": {"description": "Output only. If the App has been removed from the Project, this is the timestamp of when the App is considered expired and will be permanently deleted. After this time, the App cannot be undeleted (that is, restored to the Project). This value is only provided if the App is in the `DELETED` state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "The resource name of the AndroidApp, in the format: projects/ PROJECT_IDENTIFIER/androidApps/APP_ID * PROJECT_IDENTIFIER: the parent Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`. * APP_ID: the globally unique, Firebase-assigned identifier for the App (see [`appId`](../projects.androidApps#AndroidApp.FIELDS.app_id)).", "type": "string"}, "packageName": {"description": "Immutable. The canonical package name of the Android app as would appear in the Google Play Developer Console.", "type": "string"}, "projectId": {"description": "Output only. Immutable. A user-assigned unique identifier of the parent FirebaseProject for the `AndroidApp`.", "readOnly": true, "type": "string"}, "sha1Hashes": {"description": "The SHA1 certificate hashes for the AndroidApp.", "items": {"type": "string"}, "type": "array"}, "sha256Hashes": {"description": "The SHA256 certificate hashes for the AndroidApp.", "items": {"type": "string"}, "type": "array"}, "state": {"description": "Output only. The lifecycle state of the App.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["Unspecified state.", "The App is active.", "The App has been soft-deleted. After an App has been in the `DELETED` state for more than 30 days, it is considered expired and will be permanently deleted. Up until this time, you can restore the App by calling `Undelete` ([Android](projects.androidApps/undelete) | [iOS](projects.iosApps/undelete) | [web](projects.webApps/undelete))."], "readOnly": true, "type": "string"}}, "type": "object"}, "AndroidAppConfig": {"description": "Configuration metadata of a single Firebase App for Android.", "id": "AndroidAppConfig", "properties": {"configFileContents": {"description": "The contents of the JSON configuration file.", "format": "byte", "type": "string"}, "configFilename": {"description": "The filename that the configuration artifact for the `AndroidApp` is typically saved as. For example: `google-services.json`", "type": "string"}}, "type": "object"}, "DefaultResources": {"deprecated": true, "description": "**DEPRECATED.** _Auto-provisioning of these resources is changing, so this object no longer reliably provides information about the resources within the Project. Instead, retrieve information about each resource directly from its resource-specific API._ The default auto-provisioned resources associated with the Project.", "id": "DefaultResources", "properties": {"hostingSite": {"deprecated": true, "description": "Output only. **DEPRECATED.** _Instead, find the name of the default Firebase Hosting site using [ListSites](https://firebase.google.com/docs/reference/hosting/rest/v1beta1/projects.sites/list) within the Firebase Hosting REST API. If the default Hosting site for the Project has not yet been provisioned, the return might not contain a default site._ The name of the default Firebase Hosting site, in the format: PROJECT_ID Though rare, your `projectId` might already be used as the name for an existing Hosting site in another project (learn more about creating non-default, [additional sites](https://firebase.google.com/docs/hosting/multisites)). In these cases, your `projectId` is appended with a hyphen then five alphanumeric characters to create your default Hosting site name. For example, if your `projectId` is `myproject123`, your default Hosting site name might be: `myproject123-a5c16`", "readOnly": true, "type": "string"}, "locationId": {"deprecated": true, "description": "Output only. **DEPRECATED.** _Instead, use product-specific REST APIs to find the location of each resource in a Project. This field may not be populated, especially for newly provisioned projects after October 30, 2024._ The ID of the Project's [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location), which are resources associated with Google App Engine. The location is one of the available [Google App Engine locations](https://cloud.google.com/about/locations#region). This field is omitted if the location for default Google Cloud resources has not been set.", "readOnly": true, "type": "string"}, "realtimeDatabaseInstance": {"deprecated": true, "description": "Output only. **DEPRECATED.** _Instead, find the name of the default Realtime Database instance using the [list endpoint](https://firebase.google.com/docs/reference/rest/database/database-management/rest/v1beta/projects.locations.instances/list) within the Firebase Realtime Database REST API. If the default Realtime Database instance for a Project has not yet been provisioned, the return might not contain a default instance._ The default Firebase Realtime Database instance name, in the format: PROJECT_ID Though rare, your `projectId` might already be used as the name for an existing Realtime Database instance in another project (learn more about [database sharding](https://firebase.google.com/docs/database/usage/sharding)). In these cases, your `projectId` is appended with a hyphen then five alphanumeric characters to create your default Realtime Database instance name. For example, if your `projectId` is `myproject123`, your default database instance name might be: `myproject123-a5c16`", "readOnly": true, "type": "string"}, "storageBucket": {"deprecated": true, "description": "Output only. **DEPRECATED.** _Instead, find the name of the default Cloud Storage for Firebase bucket using the [list endpoint](https://firebase.google.com/docs/reference/rest/storage/rest/v1beta/projects.buckets/list) within the Cloud Storage for Firebase REST API. If the default bucket for the Project has not yet been provisioned, the return might not contain a default bucket._ The name of the default Cloud Storage for Firebase bucket, in one of the following formats: * If provisioned _before_ October 30, 2024: PROJECT_ID.firebasestorage.app * If provisioned _on or after_ October 30, 2024: PROJECT_ID.firebasestorage.app", "readOnly": true, "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "FinalizeDefaultLocationRequest": {"deprecated": true, "id": "FinalizeDefaultLocationRequest", "properties": {"locationId": {"description": "**DEPRECATED** The ID of the Project's [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location), which are resources associated with Google App Engine. The location must be one of the available [Google App Engine locations](https://cloud.google.com/about/locations#region).", "type": "string"}}, "type": "object"}, "FirebaseAppInfo": {"description": "A high-level summary of an App.", "id": "FirebaseAppInfo", "properties": {"apiKeyId": {"description": "The globally unique, Google-assigned identifier (UID) for the Firebase API key associated with the App. Be aware that this value is the UID of the API key, _not_ the [`keyString`](https://cloud.google.com/api-keys/docs/reference/rest/v2/projects.locations.keys#Key.FIELDS.key_string) of the API key. The `keyString` is the value that can be found in the App's configuration artifact ([`AndroidApp`](../../rest/v1beta1/projects.androidApps/getConfig) | [`IosApp`](../../rest/v1beta1/projects.iosApps/getConfig) | [`WebApp`](../../rest/v1beta1/projects.webApps/getConfig)). If `api_key_id` is not set in requests to create the App ([`AndroidApp`](../../rest/v1beta1/projects.androidApps/create) | [`IosApp`](../../rest/v1beta1/projects.iosApps/create) | [`WebApp`](../../rest/v1beta1/projects.webApps/create)), then Firebase automatically associates an `api_key_id` with the App. This auto-associated key may be an existing valid key or, if no valid key exists, a new one will be provisioned.", "type": "string"}, "appId": {"description": "Output only. Immutable. The globally unique, Firebase-assigned identifier for the `WebApp`. This identifier should be treated as an opaque token, as the data format is not specified.", "readOnly": true, "type": "string"}, "displayName": {"description": "The user-assigned display name of the Firebase App.", "type": "string"}, "expireTime": {"description": "Output only. If the App has been removed from the Project, this is the timestamp of when the App is considered expired and will be permanently deleted. After this time, the App cannot be undeleted (that is, restored to the Project). This value is only provided if the App is in the `DELETED` state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "The resource name of the Firebase App, in the format: projects/PROJECT_ID /iosApps/APP_ID or projects/PROJECT_ID/androidApps/APP_ID or projects/ PROJECT_ID/webApps/APP_ID", "type": "string"}, "namespace": {"description": "Output only. Immutable. The platform-specific identifier of the App. *Note:* For most use cases, use `appId`, which is the canonical, globally unique identifier for referencing an App. This string is derived from a native identifier for each platform: `packageName` for an `AndroidApp`, `bundleId` for an `IosApp`, and `webId` for a `WebApp`. Its contents should be treated as opaque, as the native identifier format may change as platforms evolve. This string is only unique within a `FirebaseProject` and its associated Apps.", "readOnly": true, "type": "string"}, "platform": {"description": "The platform of the Firebase App.", "enum": ["PLATFORM_UNSPECIFIED", "IOS", "ANDROID", "WEB"], "enumDescriptions": ["Unknown state. This is only used for distinguishing unset values.", "The Firebase App is associated with iOS.", "The Firebase App is associated with Android.", "The Firebase App is associated with web."], "type": "string"}, "state": {"description": "Output only. The lifecycle state of the App.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["Unspecified state.", "The App is active.", "The App has been soft-deleted. After an App has been in the `DELETED` state for more than 30 days, it is considered expired and will be permanently deleted. Up until this time, you can restore the App by calling `Undelete` ([Android](projects.androidApps/undelete) | [iOS](projects.iosApps/undelete) | [web](projects.webApps/undelete))."], "readOnly": true, "type": "string"}}, "type": "object"}, "FirebaseProject": {"description": "A `FirebaseProject` is the top-level Firebase entity. It is the container for Firebase Apps, Firebase Hosting sites, storage systems (Firebase Realtime Database, Cloud Firestore, Cloud Storage buckets), and other Firebase and Google Cloud resources. You create a `FirebaseProject` by calling AddFirebase and specifying an *existing* [Google Cloud `Project`](https://cloud.google.com/resource-manager/reference/rest/v1/projects). This adds Firebase resources to the existing Google Cloud `Project`. Since a FirebaseProject is actually also a Google Cloud `Project`, a `FirebaseProject` has the same underlying Google Cloud identifiers (`projectNumber` and `projectId`). This allows for easy interop with Google APIs.", "id": "FirebaseProject", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "A set of user-defined annotations for the FirebaseProject. Learn more about annotations in Google's [AIP-128 standard](https://google.aip.dev/128#annotations). These annotations are intended solely for developers and client-side tools. Firebase services will not mutate this annotations set.", "type": "object"}, "displayName": {"description": "The user-assigned display name of the Project.", "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and it may be sent with update requests to ensure the client has an up-to-date value before proceeding. Learn more about `etag` in Google's [AIP-154 standard](https://google.aip.dev/154#declarative-friendly-resources). This etag is strongly validated.", "type": "string"}, "name": {"description": "The resource name of the Project, in the format: projects/PROJECT_IDENTIFIER PROJECT_IDENTIFIER: the Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`.", "type": "string"}, "projectId": {"description": "Output only. Immutable. A user-assigned unique identifier for the Project. This identifier may appear in URLs or names for some Firebase resources associated with the Project, but it should generally be treated as a convenience alias to reference the Project.", "readOnly": true, "type": "string"}, "projectNumber": {"description": "Output only. Immutable. The globally unique, Google-assigned canonical identifier for the Project. Use this identifier when configuring integrations and/or making API calls to Firebase or third-party services.", "format": "int64", "readOnly": true, "type": "string"}, "resources": {"$ref": "DefaultResources", "deprecated": true, "description": "Output only. **DEPRECATED.** _Auto-provisioning of these resources is changing, so this object no longer reliably provides information about the Project. Instead, retrieve information about each resource directly from its resource-specific API._ The default Firebase resources associated with the Project.", "readOnly": true}, "state": {"description": "Output only. The lifecycle state of the Project.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["Unspecified state.", "The Project is active.", "The Project has been soft-deleted."], "readOnly": true, "type": "string"}}, "type": "object"}, "IosApp": {"description": "Details of a Firebase App for iOS.", "id": "IosApp", "properties": {"apiKeyId": {"description": "The globally unique, Google-assigned identifier (UID) for the Firebase API key associated with the `IosApp`. Be aware that this value is the UID of the API key, _not_ the [`keyString`](https://cloud.google.com/api-keys/docs/reference/rest/v2/projects.locations.keys#Key.FIELDS.key_string) of the API key. The `keyString` is the value that can be found in the App's [configuration artifact](../../rest/v1beta1/projects.iosApps/getConfig). If `api_key_id` is not set in requests to [`iosApps.Create`](../../rest/v1beta1/projects.iosApps/create), then Firebase automatically associates an `api_key_id` with the `IosApp`. This auto-associated key may be an existing valid key or, if no valid key exists, a new one will be provisioned. In patch requests, `api_key_id` cannot be set to an empty value, and the new UID must have no restrictions or only have restrictions that are valid for the associated `IosApp`. We recommend using the [Google Cloud Console](https://console.cloud.google.com/apis/credentials) to manage API keys.", "type": "string"}, "appId": {"description": "Output only. Immutable. The globally unique, Firebase-assigned identifier for the `IosApp`. This identifier should be treated as an opaque token, as the data format is not specified.", "readOnly": true, "type": "string"}, "appStoreId": {"description": "The automatically generated Apple ID assigned to the iOS app by Apple in the iOS App Store.", "type": "string"}, "bundleId": {"description": "Immutable. The canonical bundle ID of the iOS app as it would appear in the iOS AppStore.", "type": "string"}, "displayName": {"description": "The user-assigned display name for the `IosApp`.", "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and it may be sent with update requests to ensure the client has an up-to-date value before proceeding. Learn more about `etag` in Google's [AIP-154 standard](https://google.aip.dev/154#declarative-friendly-resources). This etag is strongly validated.", "type": "string"}, "expireTime": {"description": "Output only. If the App has been removed from the Project, this is the timestamp of when the App is considered expired and will be permanently deleted. After this time, the App cannot be undeleted (that is, restored to the Project). This value is only provided if the App is in the `DELETED` state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "The resource name of the IosApp, in the format: projects/PROJECT_IDENTIFIER /iosApps/APP_ID * PROJECT_IDENTIFIER: the parent Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`. * APP_ID: the globally unique, Firebase-assigned identifier for the App (see [`appId`](../projects.iosApps#IosApp.FIELDS.app_id)).", "type": "string"}, "projectId": {"description": "Output only. Immutable. A user-assigned unique identifier of the parent FirebaseProject for the `IosApp`.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The lifecycle state of the App.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["Unspecified state.", "The App is active.", "The App has been soft-deleted. After an App has been in the `DELETED` state for more than 30 days, it is considered expired and will be permanently deleted. Up until this time, you can restore the App by calling `Undelete` ([Android](projects.androidApps/undelete) | [iOS](projects.iosApps/undelete) | [web](projects.webApps/undelete))."], "readOnly": true, "type": "string"}, "teamId": {"description": "The Apple Developer Team ID associated with the App in the App Store.", "type": "string"}}, "type": "object"}, "IosAppConfig": {"description": "Configuration metadata of a single Firebase App for iOS.", "id": "IosAppConfig", "properties": {"configFileContents": {"description": "The content of the XML configuration file.", "format": "byte", "type": "string"}, "configFilename": {"description": "The filename that the configuration artifact for the `IosApp` is typically saved as. For example: `GoogleService-Info.plist`", "type": "string"}}, "type": "object"}, "ListAndroidAppsResponse": {"id": "ListAndroidAppsResponse", "properties": {"apps": {"description": "List of each `AndroidApp` associated with the specified `FirebaseProject`.", "items": {"$ref": "AndroidApp"}, "type": "array"}, "nextPageToken": {"description": "If the result list is too large to fit in a single response, then a token is returned. If the string is empty, then this response is the last page of results. This token can be used in a subsequent call to `ListAndroidApps` to find the next group of Apps. Page tokens are short-lived and should not be persisted.", "type": "string"}}, "type": "object"}, "ListAvailableLocationsResponse": {"deprecated": true, "id": "ListAvailableLocationsResponse", "properties": {"locations": {"description": "One page of results from a call to `ListAvailableLocations`.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "If the result list is too large to fit in a single response, then a token is returned. If the string is empty, then this response is the last page of results and all available locations have been listed. This token can be used in a subsequent call to `ListAvailableLocations` to find more locations. Page tokens are short-lived and should not be persisted.", "type": "string"}}, "type": "object"}, "ListAvailableProjectsResponse": {"id": "ListAvailableProjectsResponse", "properties": {"nextPageToken": {"description": "If the result list is too large to fit in a single response, then a token is returned. If the string is empty, then this response is the last page of results. This token can be used in a subsequent calls to `ListAvailableProjects` to find the next group of Projects. Page tokens are short-lived and should not be persisted.", "type": "string"}, "projectInfo": {"description": "The list of Google Cloud `Projects` which can have Firebase resources added to them.", "items": {"$ref": "ProjectInfo"}, "type": "array"}}, "type": "object"}, "ListFirebaseProjectsResponse": {"id": "ListFirebaseProjectsResponse", "properties": {"nextPageToken": {"description": "If the result list is too large to fit in a single response, then a token is returned. If the string is empty, then this response is the last page of results. This token can be used in a subsequent calls to `ListFirebaseProjects` to find the next group of Projects. Page tokens are short-lived and should not be persisted.", "type": "string"}, "results": {"description": "One page of the list of Projects that are accessible to the caller.", "items": {"$ref": "FirebaseProject"}, "type": "array"}}, "type": "object"}, "ListIosAppsResponse": {"id": "ListIosAppsResponse", "properties": {"apps": {"description": "List of each `IosApp` associated with the specified `FirebaseProject`.", "items": {"$ref": "IosApp"}, "type": "array"}, "nextPageToken": {"description": "If the result list is too large to fit in a single response, then a token is returned. If the string is empty, then this response is the last page of results. This token can be used in a subsequent call to `ListIosApps` to find the next group of Apps. Page tokens are short-lived and should not be persisted.", "type": "string"}}, "type": "object"}, "ListShaCertificatesResponse": {"id": "ListShaCertificatesResponse", "properties": {"certificates": {"description": "The list of each `ShaCertificate` associated with the `AndroidApp`.", "items": {"$ref": "ShaCertificate"}, "type": "array"}}, "type": "object"}, "ListWebAppsResponse": {"id": "ListWebAppsResponse", "properties": {"apps": {"description": "List of each `WebApp` associated with the specified `FirebaseProject`.", "items": {"$ref": "WebApp"}, "type": "array"}, "nextPageToken": {"description": "If the result list is too large to fit in a single response, then a token is returned. If the string is empty, then this response is the last page of results. This token can be used in a subsequent call to `ListWebApps` to find the next group of Apps. Page tokens are short-lived and should not be persisted.", "type": "string"}}, "type": "object"}, "Location": {"deprecated": true, "description": "**DEPRECATED.** _This Location is no longer used to determine Firebase resource locations. Instead, consult product documentation to determine valid locations for each resource used in your Project._ A [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location) that can be selected for a FirebaseProject. These are resources associated with Google App Engine.", "id": "Location", "properties": {"features": {"description": "Products and services that are available in the location for default Google Cloud resources.", "items": {"enum": ["LOCATION_FEATURE_UNSPECIFIED", "FIRESTORE", "DEFAULT_STORAGE", "FUNCTIONS"], "enumDescriptions": ["Used internally for distinguishing unset values and is not intended for external use.", "This location supports Cloud Firestore database instances. Google App Engine is available in this location, so it can be a Project's location for default Google Cloud resources.", "This location supports default Cloud Storage buckets. Google App Engine is available in this location, so it can be a Project's location for default Google Cloud resources.", "Cloud Functions for Firebase is available in this location."], "type": "string"}, "type": "array"}, "locationId": {"description": "The ID of the Project's location for default Google Cloud resources. It will be one of the available [Google App Engine locations](https://cloud.google.com/about/locations#region).", "type": "string"}, "type": {"description": "Indicates whether the location for default Google Cloud resources is a [regional or multi-regional location](https://firebase.google.com/docs/projects/locations#types) for data replication.", "enum": ["LOCATION_TYPE_UNSPECIFIED", "REGIONAL", "MULTI_REGIONAL"], "enumDescriptions": ["Used internally for distinguishing unset values and is not intended for external use.", "The location is a regional location. Data in a regional location is replicated in multiple zones within a region.", "The location is a multi-regional location. Data in a multi-region location is replicated in multiple regions. Within each region, data is replicated in multiple zones."], "type": "string"}}, "type": "object"}, "MessageSet": {"deprecated": true, "description": "This is proto2's version of MessageSet. DEPRECATED: DO NOT USE FOR NEW FIELDS. If you are using editions or proto2, please make your own extendable messages for your use case. If you are using proto3, please use `Any` instead. MessageSet was the implementation of extensions for proto1. When proto2 was introduced, extensions were implemented as a first-class feature. This schema for MessageSet was meant to be a \"bridge\" solution to migrate MessageSet-bearing messages from proto1 to proto2. This schema has been open-sourced only to facilitate the migration of Google products with MessageSet-bearing messages to open-source environments.", "id": "MessageSet", "properties": {}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Describes the progress of an LRO. It is included in the `metadata` field of the `Operation`.", "id": "OperationMetadata", "properties": {}, "type": "object"}, "ProductMetadata": {"description": "Metadata about a long-running Product operation.", "id": "ProductMetadata", "properties": {"warningMessages": {"description": "List of warnings related to the associated operation.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ProjectInfo": {"description": "A reference to a Google Cloud `Project`.", "id": "ProjectInfo", "properties": {"displayName": {"description": "The user-assigned display name of the Google Cloud `Project`, for example: `My App`.", "type": "string"}, "locationId": {"description": "**DEPRECATED** _Instead, use product-specific REST APIs to work with the location of each resource in a Project. This field may not be populated, especially for newly provisioned projects after October 30, 2024._ The ID of the Project's [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location). The location is one of the available [Google App Engine locations](https://cloud.google.com/about/locations#region). Not all Projects will have this field populated. If it is not populated, it means that the Project does not yet have a location for default Google Cloud resources.", "type": "string"}, "project": {"description": "The resource name of the Google Cloud `Project` to which Firebase resources can be added, in the format: projects/PROJECT_IDENTIFIER Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "type": "string"}}, "type": "object"}, "RemoveAnalyticsRequest": {"id": "RemoveAnalyticsRequest", "properties": {"analyticsPropertyId": {"description": "Optional. The ID of the Google Analytics property associated with the specified `FirebaseProject`. - If not set, then the Google Analytics property that is currently associated with the specified `FirebaseProject` is removed. - If set, and the specified `FirebaseProject` is currently associated with a *different* Google Analytics property, then the response is a `412 Precondition Failed` error. ", "type": "string"}}, "type": "object"}, "RemoveAndroidAppRequest": {"id": "RemoveAndroidAppRequest", "properties": {"allowMissing": {"description": "If set to true, and the App is not found, the request will succeed but no action will be taken on the server.", "type": "boolean"}, "etag": {"description": "Checksum provided in the AndroidApp resource. If provided, this checksum ensures that the client has an up-to-date value before proceeding.", "type": "string"}, "immediate": {"description": "Determines whether to _immediately_ delete the AndroidApp. If set to true, the App is immediately deleted from the Project and cannot be undeleted (that is, restored to the Project). If not set, defaults to false, which means the App will be set to expire in 30 days. Within the 30 days, the App may be restored to the Project using UndeleteAndroidApp.", "type": "boolean"}, "validateOnly": {"description": "If set to true, the request is only validated. The App will _not_ be removed.", "type": "boolean"}}, "type": "object"}, "RemoveIosAppRequest": {"id": "RemoveIosAppRequest", "properties": {"allowMissing": {"description": "If set to true, and the App is not found, the request will succeed but no action will be taken on the server.", "type": "boolean"}, "etag": {"description": "Checksum provided in the IosApp resource. If provided, this checksum ensures that the client has an up-to-date value before proceeding.", "type": "string"}, "immediate": {"description": "Determines whether to _immediately_ delete the IosApp. If set to true, the App is immediately deleted from the Project and cannot be undeleted (that is, restored to the Project). If not set, defaults to false, which means the App will be set to expire in 30 days. Within the 30 days, the App may be restored to the Project using UndeleteIosApp", "type": "boolean"}, "validateOnly": {"description": "If set to true, the request is only validated. The App will _not_ be removed.", "type": "boolean"}}, "type": "object"}, "RemoveWebAppRequest": {"id": "RemoveWebAppRequest", "properties": {"allowMissing": {"description": "If set to true, and the App is not found, the request will succeed but no action will be taken on the server.", "type": "boolean"}, "etag": {"description": "Checksum provided in the WebApp resource. If provided, this checksum ensures that the client has an up-to-date value before proceeding.", "type": "string"}, "immediate": {"description": "Determines whether to _immediately_ delete the WebApp. If set to true, the App is immediately deleted from the Project and cannot be undeleted (that is, restored to the Project). If not set, defaults to false, which means the App will be set to expire in 30 days. Within the 30 days, the App may be restored to the Project using UndeleteWebApp", "type": "boolean"}, "validateOnly": {"description": "If set to true, the request is only validated. The App will _not_ be removed.", "type": "boolean"}}, "type": "object"}, "SearchFirebaseAppsResponse": {"id": "SearchFirebaseAppsResponse", "properties": {"apps": {"description": "One page of results from a call to `SearchFirebaseApps`.", "items": {"$ref": "FirebaseAppInfo"}, "type": "array"}, "nextPageToken": {"description": "If the result list is too large to fit in a single response, then a token is returned. This token can be used in a subsequent calls to `SearchFirebaseApps` to find the next group of Apps. Page tokens are short-lived and should not be persisted.", "type": "string"}}, "type": "object"}, "ShaCertificate": {"description": "A SHA-1 or SHA-256 certificate associated with the AndroidApp.", "id": "ShaCertificate", "properties": {"certType": {"description": "The type of SHA certificate encoded in the hash.", "enum": ["SHA_CERTIFICATE_TYPE_UNSPECIFIED", "SHA_1", "SHA_256"], "enumDescriptions": ["Unknown state. This is only used for distinguishing unset values.", "Certificate is a SHA-1 type certificate.", "Certificate is a SHA-256 type certificate."], "type": "string"}, "name": {"description": "The resource name of the ShaCertificate for the AndroidApp, in the format: projects/PROJECT_IDENTIFIER/androidApps/APP_ID/sha/SHA_HASH * PROJECT_IDENTIFIER: the parent Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`. * APP_ID: the globally unique, Firebase-assigned identifier for the App (see [`appId`](../projects.androidApps#AndroidApp.FIELDS.app_id)). * SHA_HASH: the certificate hash for the App (see [`shaHash`](../projects.androidApps.sha#ShaCertificate.FIELDS.sha_hash)).", "type": "string"}, "shaHash": {"description": "The certificate hash for the `AndroidApp`.", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StatusProto": {"description": "Wire-format for a Status object", "id": "StatusProto", "properties": {"canonicalCode": {"description": "copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional int32 canonical_code = 6;", "format": "int32", "type": "integer"}, "code": {"description": "Numeric code drawn from the space specified below. Often, this is the canonical error space, and code is drawn from google3/util/task/codes.proto copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional int32 code = 1;", "format": "int32", "type": "integer"}, "message": {"description": "Detail message copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional string message = 3;", "type": "string"}, "messageSet": {"$ref": "MessageSet", "description": "message_set associates an arbitrary proto message with the status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional proto2.bridge.MessageSet message_set = 5;"}, "space": {"description": "copybara:strip_begin(b/383363683) Space to which this status belongs copybara:strip_end_and_replace optional string space = 2; // Space to which this status belongs", "type": "string"}}, "type": "object"}, "StreamMapping": {"description": "A mapping of a Firebase App to a Google Analytics data stream", "id": "StreamMapping", "properties": {"app": {"description": "The resource name of the Firebase App associated with the Google Analytics data stream, in the format: projects/PROJECT_IDENTIFIER/androidApps/APP_ID or projects/PROJECT_IDENTIFIER/iosApps/APP_ID or projects/PROJECT_IDENTIFIER /webApps/APP_ID Refer to the `FirebaseProject` [`name`](../projects#FirebaseProject.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "type": "string"}, "measurementId": {"description": "Applicable for Firebase Web Apps only. The unique Google-assigned identifier of the Google Analytics web stream associated with the Firebase Web App. Firebase SDKs use this ID to interact with Google Analytics APIs. Learn more about this ID and Google Analytics web streams in the [Analytics documentation](https://support.google.com/analytics/answer/9304153).", "type": "string"}, "streamId": {"description": "The unique Google-assigned identifier of the Google Analytics data stream associated with the Firebase App. Learn more about Google Analytics data streams in the [Analytics documentation](https://support.google.com/analytics/answer/9303323).", "format": "int64", "type": "string"}}, "type": "object"}, "UndeleteAndroidAppRequest": {"id": "UndeleteAndroidAppRequest", "properties": {"etag": {"description": "Checksum provided in the AndroidApp resource. If provided, this checksum ensures that the client has an up-to-date value before proceeding.", "type": "string"}, "validateOnly": {"description": "If set to true, the request is only validated. The App will _not_ be undeleted.", "type": "boolean"}}, "type": "object"}, "UndeleteIosAppRequest": {"id": "UndeleteIosAppRequest", "properties": {"etag": {"description": "Checksum provided in the IosApp resource. If provided, this checksum ensures that the client has an up-to-date value before proceeding.", "type": "string"}, "validateOnly": {"description": "If set to true, the request is only validated. The App will _not_ be undeleted.", "type": "boolean"}}, "type": "object"}, "UndeleteWebAppRequest": {"id": "UndeleteWebAppRequest", "properties": {"etag": {"description": "Checksum provided in the WebApp resource. If provided, this checksum ensures that the client has an up-to-date value before proceeding.", "type": "string"}, "validateOnly": {"description": "If set to true, the request is only validated. The App will _not_ be undeleted.", "type": "boolean"}}, "type": "object"}, "WebApp": {"description": "Details of a Firebase App for the web.", "id": "WebApp", "properties": {"apiKeyId": {"description": "The globally unique, Google-assigned identifier (UID) for the Firebase API key associated with the `WebApp`. Be aware that this value is the UID of the API key, _not_ the [`keyString`](https://cloud.google.com/api-keys/docs/reference/rest/v2/projects.locations.keys#Key.FIELDS.key_string) of the API key. The `keyString` is the value that can be found in the App's [configuration artifact](../../rest/v1beta1/projects.webApps/getConfig). If `api_key_id` is not set in requests to [`webApps.Create`](../../rest/v1beta1/projects.webApps/create), then Firebase automatically associates an `api_key_id` with the `WebApp`. This auto-associated key may be an existing valid key or, if no valid key exists, a new one will be provisioned. In patch requests, `api_key_id` cannot be set to an empty value, and the new UID must have no restrictions or only have restrictions that are valid for the associated `WebApp`. We recommend using the [Google Cloud Console](https://console.cloud.google.com/apis/credentials) to manage API keys.", "type": "string"}, "appId": {"description": "Output only. Immutable. The globally unique, Firebase-assigned identifier for the `WebApp`. This identifier should be treated as an opaque token, as the data format is not specified.", "readOnly": true, "type": "string"}, "appUrls": {"description": "The URLs where the `WebApp` is hosted.", "items": {"type": "string"}, "type": "array"}, "displayName": {"description": "The user-assigned display name for the `WebApp`.", "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and it may be sent with update requests to ensure the client has an up-to-date value before proceeding. Learn more about `etag` in Google's [AIP-154 standard](https://google.aip.dev/154#declarative-friendly-resources). This etag is strongly validated.", "type": "string"}, "expireTime": {"description": "Output only. If the App has been removed from the Project, this is the timestamp of when the App is considered expired and will be permanently deleted. After this time, the App cannot be undeleted (that is, restored to the Project). This value is only provided if the App is in the `DELETED` state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "The resource name of the WebApp, in the format: projects/PROJECT_IDENTIFIER /webApps/APP_ID * PROJECT_IDENTIFIER: the parent Project's [`ProjectNumber`](../projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](../projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510). Note that the value for PROJECT_IDENTIFIER in any response body will be the `ProjectId`. * APP_ID: the globally unique, Firebase-assigned identifier for the App (see [`appId`](../projects.webApps#WebApp.FIELDS.app_id)).", "type": "string"}, "projectId": {"description": "Output only. Immutable. A user-assigned unique identifier of the parent FirebaseProject for the `WebApp`.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The lifecycle state of the App.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETED"], "enumDescriptions": ["Unspecified state.", "The App is active.", "The App has been soft-deleted. After an App has been in the `DELETED` state for more than 30 days, it is considered expired and will be permanently deleted. Up until this time, you can restore the App by calling `Undelete` ([Android](projects.androidApps/undelete) | [iOS](projects.iosApps/undelete) | [web](projects.webApps/undelete))."], "readOnly": true, "type": "string"}, "webId": {"deprecated": true, "description": "Output only. Immutable. A unique, Firebase-assigned identifier for the `WebApp`. This identifier is only used to populate the `namespace` value for the `WebApp`. For most use cases, use `appId` to identify or reference the App. The `webId` value is only unique within a `FirebaseProject` and its associated Apps.", "readOnly": true, "type": "string"}}, "type": "object"}, "WebAppConfig": {"description": "Configuration metadata of a single Firebase App for the web.", "id": "WebAppConfig", "properties": {"apiKey": {"description": "The [`keyString`](https://cloud.google.com/api-keys/docs/reference/rest/v2/projects.locations.keys#Key.FIELDS.key_string) of the API key associated with the `WebApp`. Note that this value is _not_ the [`apiKeyId`](../projects.webApps#WebApp.FIELDS.api_key_id) (the UID) of the API key associated with the `WebApp`.", "type": "string"}, "appId": {"description": "Immutable. The globally unique, Firebase-assigned identifier for the `WebApp`.", "type": "string"}, "authDomain": {"description": "The domain Firebase Auth configures for OAuth redirects, in the format: PROJECT_ID.firebaseapp.com", "type": "string"}, "databaseURL": {"deprecated": true, "description": "**DEPRECATED.** _Instead, find the URL of the default Realtime Database instance using the [list endpoint](https://firebase.google.com/docs/reference/rest/database/database-management/rest/v1beta/projects.locations.instances/list) within the Firebase Realtime Database REST API. If the default instance for the Project has not yet been provisioned, the return might not contain a default instance. Note that the config that's generated for the Firebase console or the Firebase CLI uses the Realtime Database endpoint to populate this value for that config._ The URL of the default Firebase Realtime Database instance.", "type": "string"}, "locationId": {"deprecated": true, "description": "**DEPRECATED.** _Instead, use product-specific REST APIs to find the location of each resource in a Project. This field may not be populated, especially for newly provisioned projects after October 30, 2024._ The ID of the Project's [\"location for default Google Cloud resources\"](https://firebase.google.com/docs/projects/locations#default-cloud-location), which are resources associated with Google App Engine. The location is one of the available [App Engine locations](https://cloud.google.com/about/locations#region). This field is omitted if the location for default Google Cloud resources has not been set.", "type": "string"}, "measurementId": {"description": "The unique Google-assigned identifier of the Google Analytics web stream associated with the `WebApp`. Firebase SDKs use this ID to interact with Google Analytics APIs. This field is only present if the `WebApp` is linked to a web stream in a Google Analytics App + Web property. Learn more about this ID and Google Analytics web streams in the [Analytics documentation](https://support.google.com/analytics/answer/9304153). To generate a `measurementId` and link the `WebApp` with a Google Analytics web stream, call [`AddGoogleAnalytics`](../../v1beta1/projects/addGoogleAnalytics). For apps using the Firebase JavaScript SDK v7.20.0 and later, Firebase dynamically fetches the `measurementId` when your app initializes Analytics. Having this ID in your config object is optional, but it does serve as a fallback in the rare case that the dynamic fetch fails.", "type": "string"}, "messagingSenderId": {"description": "The sender ID for use with Firebase Cloud Messaging.", "type": "string"}, "projectId": {"description": "Immutable. A user-assigned unique identifier for the `FirebaseProject`.", "type": "string"}, "projectNumber": {"description": "Output only. Immutable. The globally unique, Google-assigned canonical identifier for the Project. Use this identifier when configuring integrations and/or making API calls to Google Cloud or third-party services.", "readOnly": true, "type": "string"}, "realtimeDatabaseUrl": {"description": "Optional. Duplicate field for the URL of the default Realtime Database instances (if the default instance has been provisioned). If the request asks for the V2 config format, this field will be populated instead of `realtime_database_instance_uri`.", "type": "string"}, "storageBucket": {"deprecated": true, "description": "**DEPRECATED.** _Instead, find the name of the default Cloud Storage for Firebase bucket using the [list endpoint](https://firebase.google.com/docs/reference/rest/storage/rest/v1beta/projects.buckets/list) within the Cloud Storage for Firebase REST API. If the default bucket for the Project has not yet been provisioned, the return might not contain a default bucket. Note that the config that's generated for the Firebase console or the Firebase CLI uses the Cloud Storage for Firebase endpoint to populate this value for that config._ The name of the default Cloud Storage for Firebase bucket.", "type": "string"}, "version": {"description": "Version of the config specification.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Firebase Management API", "version": "v1beta1", "version_module": true}