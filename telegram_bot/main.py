import os
import logging
import asyncio
import threading
from datetime import datetime, timed<PERSON><PERSON>
from dotenv import load_dotenv
from telegram import Update, BotCommand
from telegram.ext import Application, CommandHandler, ContextTypes
from flask import Flask, request, Response, jsonify
import json
import signal
import sys
from playwright.async_api import async_playwright
import google.generativeai as genai

from utils.db_utils import get_recent_news, get_bot_setting, set_bot_setting
from langchain_google_genai import ChatGoogleGenerativeAI

# 加载环境变量
load_dotenv(override=True)

TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
WEBHOOK_URL = os.getenv("WEBHOOK_URL") # 新增：Webhook URL
PORT = int(os.getenv("PORT", 8080)) # 新增：监听端口

# 配置日志
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO
)
logger = logging.getLogger(__name__)

# 初始化 Gemini 模型
llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash-exp",
    google_api_key=GEMINI_API_KEY,
    temperature=0.3
)

# 配置 Gemini API
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    gemini_model = genai.GenerativeModel('gemini-2.0-flash')

# 全局变量
app_instance = None
flask_app = None
shutdown_event = threading.Event()

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """发送欢迎消息"""
    welcome_text = (
        "🚀 欢迎使用加密货币新闻 Bot！\n\n"
        "📰 使用 /news 获取最近24小时新闻\n"
        "🔍 使用 /news <币种> 获取特定币种新闻\n"
        "⚡ 使用 /latest 获取最新实时新闻快讯\n"
        "ℹ️ 使用 /about 了解更多功能\n\n"
        "例如：/news btc 或 /news ethereum"
    )
    await update.message.reply_text(welcome_text)

async def news_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /news 命令"""
    try:
        # 发送处理中消息
        processing_msg = await update.message.reply_text("🔄 正在获取最新新闻，请稍候...")

        args = context.args
        coin = None
        if args:
            coin = args[0].upper()

        # 获取新闻数据
        news_list = get_recent_news(hours=24, coin=coin)

        if not news_list:
            await processing_msg.edit_text(
                f"😔 抱歉，在过去 24 小时内没有找到{'关于 ' + coin + ' ' if coin else ''}的新闻。"
            )
            return

        if coin:
            # 总结特定币种的新闻
            summaries = [news['summary'] for news in news_list
                        if news.get('summary') and news['summary'].strip()
                        and '处理失败' not in news['summary']]

            if not summaries:
                await processing_msg.edit_text(f"😔 抱歉，没有找到关于 {coin} 的可用新闻总结。")
                return

            # 限制总结内容长度
            combined_text = "\n\n".join(summaries[:5])  # 最多5条新闻
            if len(combined_text) > 3000:
                combined_text = combined_text[:3000] + "..."

            prompt = f"""请根据以下关于 {coin} 的新闻简讯，生成一个简洁的中文综合总结：

要求：
1. 突出关键信息和趋势
2. 保持客观中性
3. 总结长度控制在300字以内

新闻内容：
{combined_text}

综合总结："""

            try:
                response = await asyncio.to_thread(llm.invoke, prompt)
                summary_text = response.content.strip()

                result_text = f"📊 **{coin} 最近 24 小时资讯总结**\n\n{summary_text}\n\n📈 基于 {len(summaries)} 条新闻生成"
                await processing_msg.edit_text(result_text, parse_mode='Markdown')

            except Exception as e:
                logger.error(f"Gemini 总结失败: {e}")
                await processing_msg.edit_text("❌ 总结新闻时发生错误，请稍后再试。")
        else:
            # 返回所有新闻简讯
            valid_news = [news for news in news_list
                         if news.get('summary') and news['summary'].strip()
                         and '处理失败' not in news['summary']]

            if not valid_news:
                await processing_msg.edit_text("😔 暂时没有可用的新闻总结。")
                return

            response_text = "📰 **最近 24 小时加密货币新闻**\n\n"

            # 限制显示数量，避免消息过长
            display_count = min(len(valid_news), 10)

            for i, news in enumerate(valid_news[:display_count]):
                title = news.get('title', '无标题')[:100]  # 限制标题长度
                summary = news.get('summary', '无总结')[:200]  # 限制总结长度
                published_at = news.get('published_at', datetime.now()).strftime('%m-%d %H:%M')
                source = news.get('source', '未知来源')

                response_text += f"**{i+1}. {title}**\n"
                response_text += f"💬 {summary}\n"
                response_text += f"📅 {source} | {published_at}\n\n"

            if len(valid_news) > display_count:
                response_text += f"... 还有 {len(valid_news) - display_count} 条新闻"

            # 分段发送长消息
            await processing_msg.delete()
            if len(response_text) > 4096:
                chunks = [response_text[i:i+4096] for i in range(0, len(response_text), 4096)]
                for chunk in chunks:
                    await update.message.reply_text(chunk, parse_mode='Markdown')
            else:
                await update.message.reply_text(response_text, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"处理 /news 命令失败: {e}")
        await update.message.reply_text("❌ 处理请求时发生错误，请稍后再试。")

async def about_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /about 命令，返回系统功能说明"""
    try:
        about_info = get_bot_setting("about_command_response")
        if about_info:
            await update.message.reply_text(about_info, parse_mode='Markdown')
        else:
            # 如果数据库中没有，设置默认值
            default_about_info = (
                "🤖 **nyxn-ai 加密货币新闻系统**\n\n"
                "📋 **主要功能：**\n"
                "• 📰 实时获取加密货币新闻\n"
                "• 🤖 AI 智能总结新闻内容\n"
                "• 🔍 支持特定币种新闻查询\n\n"
                "💡 **使用方法：**\n"
                "• `/news` - 获取最近24小时热门新闻\n"
                "• `/news btc` - 获取比特币相关新闻总结\n"
                "• `/news ethereum` - 获取以太坊相关新闻\n"
                "• `/latest` - 获取最新实时新闻快讯\n\n"
                "⚡ **技术架构：**\n"
                "• 数据源：Cryptopanic 网页抓取\n"
                "• 内容抓取：Playwright\n"
                "• AI 总结：Google Gemini\n"
                "• 数据存储：MySQL 数据库\n\n"
                "🔄 实时获取最新新闻内容"
            )
            # 保存到数据库
            set_bot_setting("about_command_response", default_about_info)
            await update.message.reply_text(default_about_info, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"处理 /about 命令失败: {e}")
        await update.message.reply_text("❌ 获取系统信息时发生错误。")

async def scrape_latest_news():
    """抓取最新新闻"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()

        try:
            logger.info("开始抓取最新新闻...")
            await page.goto('https://cryptopanic.com/')
            await page.wait_for_load_state('networkidle')

            # 查找新闻项目
            news_items = await page.query_selector_all('.news-row')
            logger.info(f"找到 {len(news_items)} 条新闻")

            news_data = []

            # 只处理前5条新闻
            for i, item in enumerate(news_items[:5]):
                try:
                    # 获取新闻标题和链接
                    link_elem = await item.query_selector('a[href*="/news/"]')
                    if not link_elem:
                        continue

                    title = await link_elem.inner_text()
                    href = await link_elem.get_attribute('href')

                    if not title or not href:
                        continue

                    # 构建完整URL
                    full_url = href if href.startswith('http') else f"https://cryptopanic.com{href}"

                    # 获取时间信息
                    time_elem = await item.query_selector('.time, .timestamp')
                    time_text = "刚刚"
                    if time_elem:
                        time_text = await time_elem.inner_text()

                    # 获取来源
                    source_elem = await item.query_selector('.source, .domain')
                    source = "CryptoPanic"
                    if source_elem:
                        source_text = await source_elem.inner_text()
                        if source_text.strip():
                            source = source_text.strip()

                    news_data.append({
                        'title': title.strip(),
                        'url': full_url,
                        'source': source,
                        'time': time_text
                    })

                except Exception as e:
                    logger.error(f"解析新闻项失败: {e}")
                    continue

            await browser.close()
            return news_data

        except Exception as e:
            logger.error(f"抓取新闻失败: {e}")
            await browser.close()
            return []

async def get_news_content_and_summarize(news_item):
    """获取新闻内容并总结"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()

        try:
            # 访问新闻详情页
            await page.goto(news_item['url'], timeout=30000)
            await page.wait_for_load_state('networkidle')

            # 获取页面内容
            content = await page.inner_text('body')

            # 清理内容
            lines = content.split('\n')
            cleaned_lines = []
            for line in lines:
                line = line.strip()
                if line and len(line) > 10:
                    cleaned_lines.append(line)

            final_content = '\n'.join(cleaned_lines[:30])  # 限制行数

            await browser.close()

            if not final_content or len(final_content) < 100:
                return "无法获取有效内容"

            # 使用Gemini进行总结
            if GEMINI_API_KEY:
                try:
                    prompt = f"""请总结以下加密货币新闻内容，生成一个简洁的中文简讯。

要求：
1. 重点突出加密货币相关信息
2. 保持客观中性的语调
3. 简讯长度控制在150字以内

新闻标题：{news_item['title']}

新闻内容：
{final_content[:2000]}

简讯总结："""

                    response = gemini_model.generate_content(prompt)
                    summary = response.text.strip()
                    return summary

                except Exception as e:
                    logger.error(f"AI总结失败: {e}")
                    return f"总结失败，原标题：{news_item['title']}"
            else:
                return f"未配置AI，原标题：{news_item['title']}"

        except Exception as e:
            logger.error(f"获取内容失败: {e}")
            await browser.close()
            return f"内容获取失败：{news_item['title']}"

async def latest_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /latest 命令，获取最新新闻快讯"""
    try:
        # 发送处理中消息
        processing_msg = await update.message.reply_text("🔄 正在获取最新新闻快讯，请稍候...")

        # 抓取最新新闻
        news_data = await scrape_latest_news()

        if not news_data:
            await processing_msg.edit_text("😔 抱歉，暂时无法获取最新新闻。")
            return

        # 更新状态
        await processing_msg.edit_text(f"📰 找到 {len(news_data)} 条最新新闻，正在生成总结...")

        # 获取前2条新闻的详细内容和总结
        summaries = []
        for i, news in enumerate(news_data[:2]):
            try:
                logger.info(f"正在处理第{i+1}条新闻...")
                summary = await get_news_content_and_summarize(news)
                summaries.append({
                    'title': news['title'],
                    'summary': summary,
                    'source': news['source'],
                    'time': news['time'],
                    'url': news['url']
                })
            except Exception as e:
                logger.error(f"处理第{i+1}条新闻失败: {e}")

        # 构建回复消息
        if summaries:
            response_text = f"📊 **最新加密货币新闻快讯**\n\n"
            response_text += f"🔍 **找到 {len(news_data)} 条最新新闻**\n\n"

            for i, item in enumerate(summaries):
                response_text += f"**{i+1}. {item['title'][:50]}...**\n"
                response_text += f"📝 {item['summary']}\n"
                response_text += f"📅 {item['source']} | {item['time']}\n\n"

            response_text += f"⏰ 更新时间: {datetime.now().strftime('%H:%M:%S')}"

            await processing_msg.edit_text(response_text, parse_mode='Markdown')
        else:
            await processing_msg.edit_text("😔 抱歉，无法生成新闻总结。")

    except Exception as e:
        logger.error(f"处理 /latest 命令失败: {e}")
        await update.message.reply_text("❌ 获取最新新闻时发生错误，请稍后再试。")

class TelegramBotServer:
    """Telegram Bot 服务器类"""

    def __init__(self):
        self.app_instance = None
        self.flask_app = None
        self.webhook_thread = None
        self.is_running = False

    def create_flask_app(self):
        """创建 Flask 应用"""
        app = Flask(__name__)

        @app.route("/webhook", methods=["POST"])
        def webhook():
            """处理 Telegram Webhook"""
            try:
                if not self.app_instance:
                    logger.error("Telegram Application 未初始化")
                    return Response(status=503)

                data = request.get_json(force=True)
                if not data:
                    logger.warning("收到空的 webhook 数据")
                    return Response(status=400)

                logger.debug(f"收到 webhook 数据: {json.dumps(data, indent=2)}")

                # 创建 Update 对象
                update = Update.de_json(data, self.app_instance.bot)
                if not update:
                    logger.warning("无法解析 Update 对象")
                    return Response(status=400)

                # 异步处理更新
                self._process_update_async(update)

                return Response(status=200)

            except Exception as e:
                logger.error(f"Webhook 处理失败: {e}", exc_info=True)
                return Response(status=500)

        @app.route("/health", methods=["GET"])
        def health_check():
            """健康检查端点"""
            try:
                status = {
                    "status": "ok",
                    "service": "telegram-bot",
                    "webhook_configured": bool(self.app_instance),
                    "timestamp": datetime.now().isoformat()
                }

                # 检查 webhook 状态
                if self.app_instance:
                    try:
                        # 这里可以添加更多健康检查逻辑
                        status["bot_username"] = self.app_instance.bot.username if hasattr(self.app_instance.bot, 'username') else "unknown"
                    except:
                        pass

                return jsonify(status)
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
                return jsonify({"status": "error", "message": str(e)}), 500

        @app.route("/webhook/info", methods=["GET"])
        def webhook_info():
            """获取 webhook 信息"""
            try:
                if not self.app_instance:
                    return jsonify({"error": "Bot not initialized"}), 503

                # 这里可以返回 webhook 配置信息
                return jsonify({
                    "webhook_url": WEBHOOK_URL,
                    "port": PORT,
                    "status": "configured"
                })
            except Exception as e:
                logger.error(f"获取 webhook 信息失败: {e}")
            return jsonify({"error": str(e)}), 500

        return app

    def _process_update_async(self, update):
        """异步处理 Telegram 更新"""
        def run_async():
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # 处理更新
                    loop.run_until_complete(self.app_instance.process_update(update))
                    logger.debug("Update 处理完成")
                except Exception as e:
                    logger.error(f"处理 Update 失败: {e}", exc_info=True)
                finally:
                    loop.close()

            except Exception as e:
                logger.error(f"异步处理失败: {e}", exc_info=True)

        # 在后台线程中运行
        thread = threading.Thread(target=run_async, daemon=True)
        thread.start()

    async def setup_webhook(self):
        """设置 Webhook"""
        try:
            # 删除旧的 webhook (如果存在)
            try:
                await self.app_instance.bot.delete_webhook()
                logger.info("旧的 Webhook 已删除。")
            except Exception as e:
                logger.warning(f"删除旧 Webhook 失败 (可能不存在): {e}")

            # 设置新的 webhook
            webhook_full_url = f"{WEBHOOK_URL.rstrip('/')}/webhook"
            await self.app_instance.bot.set_webhook(url=webhook_full_url)
            webhook_info = await self.app_instance.bot.get_webhook_info()
            logger.info(f"Webhook 已成功设置到: {webhook_full_url}")
            logger.info(f"Webhook 信息: {webhook_info}")

            if webhook_info.last_error_date:
                logger.warning(f"  Webhook 上次错误: {webhook_info.last_error_message}")

            return True

        except Exception as e:
            logger.error(f"Webhook 设置失败: {e}", exc_info=True)
            return False

    async def initialize_bot(self):
        """初始化 Telegram Bot"""
        try:
            # 创建 Application 实例
            self.app_instance = Application.builder().token(TELEGRAM_BOT_TOKEN).build()

            # 注册命令处理程序
            self.app_instance.add_handler(CommandHandler("start", start))
            self.app_instance.add_handler(CommandHandler("news", news_command))
            self.app_instance.add_handler(CommandHandler("about", about_command))
            self.app_instance.add_handler(CommandHandler("latest", latest_command))

            # 初始化 Application
            await self.app_instance.initialize()
            logger.info("Telegram Bot Application 初始化成功")

            # 设置Bot命令菜单
            await self.setup_bot_commands()

            return True

        except Exception as e:
            logger.error(f"Bot 初始化失败: {e}", exc_info=True)
            return False

    async def setup_bot_commands(self):
        """设置Bot命令菜单"""
        try:
            commands = [
                BotCommand("start", "开始使用Bot"),
                BotCommand("news", "获取最近24小时加密货币新闻"),
                BotCommand("latest", "获取最新实时新闻快讯"),
                BotCommand("about", "了解系统功能和使用方法")
            ]

            await self.app_instance.bot.set_my_commands(commands)
            logger.info("Bot命令菜单设置成功")

        except Exception as e:
            logger.error(f"设置Bot命令菜单失败: {e}", exc_info=True)

    def start_server(self):
        """启动服务器"""
        try:
            self.is_running = True

            # 创建 Flask 应用
            self.flask_app = self.create_flask_app()

            logger.info(f"启动 Flask 服务器，监听 0.0.0.0:{PORT}")

            # 启动 Flask 服务器
            self.flask_app.run(
                host="0.0.0.0",
                port=PORT,
                debug=False,
                threaded=True,
                use_reloader=False
            )

        except Exception as e:
            logger.error(f"服务器启动失败: {e}", exc_info=True)
            raise

    async def shutdown(self):
        """关闭服务"""
        try:
            self.is_running = False

            if self.app_instance:
                logger.info("关闭 Telegram Bot...")
                # 删除 webhook
                try:
                    await self.app_instance.bot.delete_webhook()
                except:
                    pass

                # 关闭 Application
                await self.app_instance.shutdown()

            logger.info("服务关闭完成")

        except Exception as e:
            logger.error(f"关闭服务失败: {e}")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，准备关闭服务...")
    shutdown_event.set()

async def main_async():
    """异步主函数"""
    # 参数验证
    if not TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN 未设置。请在 .env 文件中配置。")
        return False

    if not WEBHOOK_URL:
        logger.error("WEBHOOK_URL 未设置。请在 .env 文件中配置。")
        return False

    # 创建服务器实例
    server = TelegramBotServer()

    try:
        # 初始化 Bot
        logger.info("初始化 Telegram Bot...")
        if not await server.initialize_bot():
            logger.error("Bot 初始化失败")
            return False

        # 设置 Webhook
        logger.info("设置 Webhook...")
        if not await server.setup_webhook():
            logger.error("Webhook 设置失败")
            return False

        logger.info("✅ Telegram Bot 初始化完成，准备启动服务器...")
        return server

    except Exception as e:
        logger.error(f"初始化失败: {e}", exc_info=True)
        await server.shutdown()
        return False

def main():
    """主函数"""
    logger.info("🚀 启动 Telegram Bot 服务...")

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 异步初始化
        server = asyncio.run(main_async())
        if not server:
            logger.error("❌ 服务初始化失败")
            sys.exit(1)

        # 启动 Flask 服务器（阻塞）
        logger.info(f"🌐 启动 Web 服务器，监听端口 {PORT}")
        server.start_server()

    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"服务运行失败: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("正在关闭服务...")
        try:
            if 'server' in locals():
                asyncio.run(server.shutdown())
        except:
            pass
        logger.info("👋 服务已关闭")

if __name__ == "__main__":
    main()
