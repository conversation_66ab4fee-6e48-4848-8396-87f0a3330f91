import os
import logging
import asyncio
import threading
from datetime import datetime, timed<PERSON><PERSON>
from dotenv import load_dotenv
from telegram import Update, BotCommand
from telegram.ext import Application, CommandHandler, ContextTypes
from flask import Flask, request, Response, jsonify
import json
import signal
import sys
from playwright.async_api import async_playwright
import google.generativeai as genai

from utils.db_utils import get_recent_news, get_bot_setting, set_bot_setting
from langchain_google_genai import ChatGoogleGenerativeAI

# 加载环境变量
load_dotenv(override=True)

TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
WEBHOOK_URL = os.getenv("WEBHOOK_URL") # 新增：Webhook URL
PORT = int(os.getenv("PORT", 8080)) # 新增：监听端口

# 配置日志
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO
)
logger = logging.getLogger(__name__)

# 初始化 Gemini 模型
llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash-exp",
    google_api_key=GEMINI_API_KEY,
    temperature=0.3
)

# 配置 Gemini API
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    gemini_model = genai.GenerativeModel('gemini-2.0-flash')

# 全局变量
app_instance = None
flask_app = None
shutdown_event = threading.Event()

class CryptoPanicScraper:
    """CryptoPanic新闻抓取器"""

    def __init__(self):
        self.base_url = "https://cryptopanic.com"

    async def scrape_latest_news(self, limit=10):
        """抓取最新新闻"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()

            try:
                logger.info("开始抓取Cryptopanic最新新闻...")
                await page.goto(self.base_url)
                await page.wait_for_load_state('networkidle')

                # 查找新闻项目 - 使用更准确的选择器
                news_items = await page.query_selector_all('.news-row')
                logger.info(f"找到 {len(news_items)} 条新闻")

                news_data = []

                # 只处理指定数量的新闻
                for i, item in enumerate(news_items[:limit]):
                    try:
                        # 获取新闻标题和链接
                        link_elem = await item.query_selector('a[href*="/news/"]')
                        if not link_elem:
                            continue

                        title = await link_elem.inner_text()
                        href = await link_elem.get_attribute('href')

                        if not title or not href:
                            continue

                        # 构建完整URL
                        full_url = href if href.startswith('http') else f"{self.base_url}{href}"

                        # 获取时间信息
                        time_elem = await item.query_selector('.time, .timestamp')
                        time_text = "刚刚"
                        if time_elem:
                            time_text = await time_elem.inner_text()

                        # 获取来源
                        source_elem = await item.query_selector('.source, .domain')
                        source = "CryptoPanic"
                        if source_elem:
                            source_text = await source_elem.inner_text()
                            if source_text.strip():
                                source = source_text.strip()

                        news_item = {
                            'title': title.strip(),
                            'url': full_url,
                            'source': source,
                            'time': time_text,
                            'published_at': datetime.now()
                        }

                        news_data.append(news_item)
                        logger.info(f"抓取新闻 {i+1}: {title[:50]}...")

                    except Exception as e:
                        logger.error(f"解析新闻项 {i+1} 失败: {e}")
                        continue

                await browser.close()
                logger.info(f"成功抓取 {len(news_data)} 条新闻")
                return news_data

            except Exception as e:
                logger.error(f"抓取新闻失败: {e}")
                await browser.close()
                return []

    async def get_news_content(self, news_url, timeout=15):
        """获取新闻详细内容"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()

            try:
                # 设置较短的超时时间
                await page.goto(news_url, timeout=timeout * 1000)
                await page.wait_for_load_state('domcontentloaded', timeout=10000)

                # 获取页面内容
                content = await page.inner_text('body')

                # 清理和过滤内容
                lines = content.split('\n')
                cleaned_lines = []

                # 定义需要过滤的关键词（扩展版）
                filter_keywords = [
                    'privacy policy', 'cookie policy', 'terms of service', 'terms and conditions',
                    'subscribe', 'newsletter', 'advertisement', 'sponsored', 'disclaimer',
                    'cryptopanic', 'privacy statement', 'data protection', 'gdpr',
                    '隐私政策', '服务条款', '免责声明', '广告', '订阅', '通讯',
                    'accept cookies', 'cookie consent', 'privacy notice', 'legal notice',
                    'follow us', 'social media', 'share this', 'related articles',
                    'read more', 'continue reading', 'click here', 'sign up',
                    'download', 'install', 'app store', 'google play', 'mobile app',
                    'join our', 'community', 'discord', 'telegram channel', 'twitter',
                    'facebook', 'instagram', 'youtube', 'linkedin', 'reddit',
                    'notification', 'alert', 'push notification', 'email alert',
                    'premium', 'pro', 'upgrade', 'subscription', 'free trial',
                    'contact us', 'support', 'help center', 'faq', 'about us',
                    'we use cookies', 'this website uses', 'by continuing',
                    'accept all', 'manage cookies', 'cookie settings'
                ]

                for line in lines:
                    line = line.strip()
                    if not line or len(line) < 15:  # 过滤太短的行
                        continue

                    # 检查是否包含过滤关键词
                    line_lower = line.lower()
                    should_filter = False
                    for keyword in filter_keywords:
                        if keyword in line_lower:
                            should_filter = True
                            break

                    if not should_filter:
                        cleaned_lines.append(line)

                # 限制内容长度，只取前20行有效内容
                final_content = '\n'.join(cleaned_lines[:20])

                await browser.close()
                return final_content if final_content.strip() else None

            except Exception as e:
                logger.error(f"获取内容失败: {e}")
                await browser.close()
                return None

    async def summarize_news(self, title, content=None):
        """使用AI总结新闻"""
        if not GEMINI_API_KEY:
            return f"📰 {title[:80]}..."

        try:
            if content and len(content) > 50:
                # 有内容时的简化总结
                prompt = f"""用一句话总结这个加密货币新闻的核心要点（不超过40字）：

标题：{title}
内容：{content[:800]}

总结："""
            else:
                # 仅基于标题的简化总结
                prompt = f"""用一句话概括这个加密货币新闻标题的核心信息（不超过30字）：

标题：{title}

概括："""

            response = gemini_model.generate_content(prompt)
            summary = response.text.strip()

            # 清理总结，移除多余的前缀
            summary = summary.replace("总结：", "").replace("概括：", "").replace("一句话", "").strip()

            return f"� {summary}"

        except Exception as e:
            logger.error(f"AI总结失败: {e}")
            return f"📰 {title[:80]}..."

# 创建全局抓取器实例
crypto_scraper = CryptoPanicScraper()

# 全局用户状态管理
welcomed_users = set()

async def send_welcome_if_needed(update: Update) -> bool:
    """如果用户是首次使用，发送简单欢迎信息并激活对话"""
    user_id = update.effective_user.id
    if user_id not in welcomed_users:
        try:
            # 发送简短的首次使用提示
            welcome_text = (
                "👋 **欢迎使用加密货币新闻Bot！**\n\n"
                "💡 **快速开始：**\n"
                "• `/news` - 24小时新闻总结\n"
                "• `/latest` - 最新实时快讯\n"
                "• `/about` - 了解更多功能\n\n"
                "🔄 正在处理您的请求..."
            )
            await update.message.reply_text(welcome_text, parse_mode='Markdown')
            welcomed_users.add(user_id)

            # 记录用户激活
            logger.info(f"用户 {user_id} 首次使用，已激活对话")
            return True
        except Exception as e:
            logger.error(f"发送欢迎信息失败: {e}")
            # 即使失败也标记为已欢迎，避免重复尝试
            welcomed_users.add(user_id)
            return True
    return False

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """发送欢迎消息"""
    # 强制发送欢迎信息（即使用户之前已经使用过）
    user_id = update.effective_user.id
    welcomed_users.add(user_id)  # 标记为已欢迎

    welcome_text = (
        "🚀 **欢迎使用加密货币新闻Bot！**\n\n"
        "🤖 **我可以为您提供：**\n"
        "• 📰 最近24小时新闻总结\n"
        "• ⚡ 最新实时新闻快讯\n"
        "• 🔍 特定币种新闻查询\n"
        "• 🤖 AI智能总结\n\n"
        "💡 **使用方法：**\n"
        "• `/news` - 获取最近24小时新闻\n"
        "• `/news btc` - 获取比特币相关新闻\n"
        "• `/latest` - 获取最新实时新闻快讯\n"
        "• `/about` - 了解系统功能\n\n"
        "🔥 **立即开始使用任何命令！**"
    )
    await update.message.reply_text(welcome_text, parse_mode='Markdown')

async def news_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /news 命令"""
    try:
        # 检查是否首次使用，如果是则发送欢迎信息
        is_first_time = await send_welcome_if_needed(update)

        # 如果是首次使用，稍作延迟让欢迎信息先显示
        if is_first_time:
            await asyncio.sleep(1)

        # 发送处理中消息
        processing_msg = await update.message.reply_text("🔄 正在获取新闻，请稍候...")

        args = context.args
        coin = None
        if args:
            coin = args[0].upper()

        # 获取新闻数据
        news_list = get_recent_news(hours=24, coin=coin)

        if not news_list:
            await processing_msg.edit_text(
                f"😔 抱歉，在过去 24 小时内没有找到{'关于 ' + coin + ' ' if coin else ''}的新闻。"
            )
            return

        if coin:
            # 特定币种的新闻处理
            if not news_list:
                await processing_msg.edit_text(f"😔 抱歉，在过去 24 小时内没有找到关于 {coin} 的新闻。")
                return

            # 更新状态
            await processing_msg.edit_text(f"📊 找到 {len(news_list)} 条关于 {coin} 的新闻，正在生成综合总结...")

            # 收集所有相关新闻的标题和总结
            news_content = []
            for news in news_list[:10]:  # 最多处理10条
                title = news.get('title', '')
                summary = news.get('summary', '')
                if summary and '处理失败' not in summary:
                    news_content.append(f"标题: {title}\n总结: {summary}")
                else:
                    news_content.append(f"标题: {title}")

            if not news_content:
                await processing_msg.edit_text(f"😔 抱歉，没有找到关于 {coin} 的可用新闻内容。")
                return

            # 生成综合总结
            combined_text = "\n\n".join(news_content)
            if len(combined_text) > 3000:
                combined_text = combined_text[:3000] + "..."

            prompt = f"""根据以下关于 {coin} 的新闻，生成简洁的中文总结（200字以内）：

重点：价格变动、技术发展、市场动态
要求：客观简洁，突出核心信息

新闻信息：
{combined_text}

总结："""

            try:
                response = await asyncio.to_thread(llm.invoke, prompt)
                summary_text = response.content.strip()

                result_text = f"📊 **{coin.upper()} 最近 24 小时资讯总结**\n\n{summary_text}\n\n📈 基于 {len(news_list)} 条新闻生成"
                await processing_msg.edit_text(result_text, parse_mode='Markdown')

            except Exception as e:
                logger.error(f"Gemini 总结失败: {e}")
                await processing_msg.edit_text("❌ 生成总结时发生错误，请稍后再试。")
        else:
            # 返回所有新闻简讯
            if not news_list:
                await processing_msg.edit_text("😔 暂时没有最近24小时的新闻数据。")
                return

            # 过滤有效新闻
            valid_news = [news for news in news_list
                         if news.get('summary') and news['summary'].strip()
                         and '处理失败' not in news['summary']]

            if not valid_news:
                await processing_msg.edit_text("😔 暂时没有可用的新闻总结。请稍后再试或使用 /latest 获取最新新闻。")
                return

            # 更新状态，准备生成综合总结
            await processing_msg.edit_text(f"📊 找到 {len(valid_news)} 条新闻，正在生成24小时综合总结...")

            # 生成24小时新闻综合总结
            try:
                # 收集所有新闻的标题和总结
                news_summaries = []
                for news in valid_news[:15]:  # 最多使用15条新闻生成综合总结
                    title = news.get('title', '')
                    summary = news.get('summary', '')
                    if summary and len(summary) > 10:
                        news_summaries.append(f"• {title}: {summary}")
                    else:
                        news_summaries.append(f"• {title}")

                combined_content = "\n".join(news_summaries)
                if len(combined_content) > 4000:
                    combined_content = combined_content[:4000] + "..."

                # 生成综合总结
                comprehensive_prompt = f"""根据以下24小时加密货币新闻，生成简洁的中文总结（150字以内）：

重点关注：价格变动、重大事件、市场趋势
要求：客观简洁，突出核心信息

新闻内容：
{combined_content}

总结："""

                if GEMINI_API_KEY:
                    response = gemini_model.generate_content(comprehensive_prompt)
                    comprehensive_summary = response.text.strip()
                else:
                    comprehensive_summary = "AI总结功能暂不可用，请查看下方详细新闻。"

                # 删除处理消息
                await processing_msg.delete()

                # 发送综合总结
                summary_header = f"📊 **最近24小时加密货币新闻综合总结**\n\n"
                summary_text = f"{comprehensive_summary}\n\n"
                summary_footer = f"📈 基于 {len(valid_news)} 条新闻生成 | ⏰ {datetime.now().strftime('%H:%M')}"

                full_summary = summary_header + summary_text + summary_footer
                await update.message.reply_text(full_summary, parse_mode='Markdown')

                # 发送详细新闻列表（简化版）
                detail_header = f"📰 **重点新闻** (前5条)\n"
                await update.message.reply_text(detail_header, parse_mode='Markdown')

                # 分批发送前5条新闻，每批2条
                batch_size = 2
                for batch_start in range(0, min(len(valid_news), 5), batch_size):
                    batch_end = min(batch_start + batch_size, min(len(valid_news), 5))
                    batch_news = valid_news[batch_start:batch_end]

                    batch_text = ""
                    for i, news in enumerate(batch_news):
                        news_index = batch_start + i + 1
                        title = news.get('title', '无标题')[:50]  # 更短的标题
                        summary = news.get('summary', '无总结')[:60]  # 更短的总结
                        published_at = news.get('published_at', datetime.now()).strftime('%m-%d %H:%M')
                        source = news.get('source', '未知来源')

                        batch_text += f"**{news_index}. {title}**\n{summary}\n📅 {source} | {published_at}\n\n"

                    try:
                        await update.message.reply_text(batch_text, parse_mode='Markdown')
                    except Exception as e:
                        logger.error(f"发送重点新闻批次失败: {e}")
                        # 发送纯文本版本
                        await update.message.reply_text(batch_text)

                    # 短暂延迟
                    await asyncio.sleep(0.3)

            except Exception as e:
                logger.error(f"生成综合总结失败: {e}")
                # 降级到原有的分批发送模式
                await processing_msg.delete()

                header = f"📰 **最近 24 小时加密货币新闻**\n\n🔍 **找到 {len(valid_news)} 条已处理的新闻**\n"
                await update.message.reply_text(header, parse_mode='Markdown')

                # 分批发送新闻，每批最多5条
                batch_size = 5
                for batch_start in range(0, min(len(valid_news), 10), batch_size):
                    batch_end = min(batch_start + batch_size, len(valid_news))
                    batch_news = valid_news[batch_start:batch_end]

                    batch_text = ""
                    for i, news in enumerate(batch_news):
                        news_index = batch_start + i + 1
                        title = news.get('title', '无标题')[:60]
                        summary = news.get('summary', '无总结')[:100]
                        published_at = news.get('published_at', datetime.now()).strftime('%m-%d %H:%M')
                        source = news.get('source', '未知来源')

                        batch_text += f"**{news_index}. {title}**\n"
                        batch_text += f"{summary}\n"
                        batch_text += f"📅 {source} | {published_at}\n\n"

                    try:
                        await update.message.reply_text(batch_text, parse_mode='Markdown')
                    except Exception as e:
                        logger.error(f"发送批次失败: {e}")
                        await update.message.reply_text(batch_text)

    except Exception as e:
        logger.error(f"处理 /news 命令失败: {e}")
        await update.message.reply_text("❌ 处理请求时发生错误，请稍后再试。")

async def about_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /about 命令，返回系统功能说明"""
    try:
        # 检查是否首次使用，如果是则发送欢迎信息
        is_first_time = await send_welcome_if_needed(update)

        # 如果是首次使用，稍作延迟让欢迎信息先显示
        if is_first_time:
            await asyncio.sleep(1)

        about_info = (
            "🤖 **nyxn-ai 加密货币新闻系统**\n\n"
            "📋 **主要功能：**\n"
            "• 📰 实时获取加密货币新闻\n"
            "• 🤖 AI 智能总结新闻内容\n"
            "• 🔍 支持特定币种新闻查询\n\n"
            "💡 **使用方法：**\n"
            "• `/news` - 获取最近24小时热门新闻\n"
            "• `/news btc` - 获取比特币相关新闻总结\n"
            "• `/news ethereum` - 获取以太坊相关新闻\n"
            "• `/latest` - 获取最新实时新闻快讯\n\n"
            "⚡ **技术架构：**\n"
            "• 数据源：Cryptopanic 网页抓取\n"
            "• 内容抓取：Playwright\n"
            "• AI 总结：Google Gemini-2.0-Flash\n"
            "• 数据存储：MySQL 数据库\n\n"
            "🔄 实时获取最新新闻内容\n"
            "🌐 Webhook: hook.nyxn.ai"
        )

        await update.message.reply_text(about_info, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"处理 /about 命令失败: {e}")
        await update.message.reply_text("❌ 获取系统信息时发生错误。")

# 删除旧的抓取函数，使用CryptoPanicScraper类

# 删除旧的内容获取和总结函数，使用CryptoPanicScraper类

async def handle_general_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理非命令消息，引导用户使用正确的命令"""
    try:
        # 检查是否首次使用，如果是则发送欢迎信息
        is_first_time = await send_welcome_if_needed(update)

        if is_first_time:
            # 首次用户，发送完整的使用指南
            await asyncio.sleep(1)

        # 引导用户使用正确的命令
        help_text = (
            "💡 **请使用以下命令：**\n\n"
            "• `/news` - 获取最近24小时新闻总结\n"
            "• `/latest` - 获取最新实时新闻快讯\n"
            "• `/about` - 了解系统功能\n"
            "• `/start` - 查看完整使用指南\n\n"
            "🔥 **直接点击上面的命令即可使用！**"
        )
        await update.message.reply_text(help_text, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"处理通用消息失败: {e}")
        try:
            await update.message.reply_text("请使用 /news、/latest 或 /about 命令获取新闻信息。")
        except:
            pass

async def latest_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /latest 命令，获取最新新闻快讯"""
    try:
        # 检查是否首次使用，如果是则发送欢迎信息
        is_first_time = await send_welcome_if_needed(update)

        # 如果是首次使用，稍作延迟让欢迎信息先显示
        if is_first_time:
            await asyncio.sleep(1)

        # 发送处理中消息
        processing_msg = await update.message.reply_text("⚡ 正在获取最新快讯...")

        # 使用CryptoPanicScraper抓取最新新闻
        news_data = await crypto_scraper.scrape_latest_news(limit=5)  # 减少到5条

        if not news_data:
            await processing_msg.edit_text("😔 抱歉，暂时无法获取最新新闻。")
            return

        # 更新状态
        await processing_msg.edit_text(f"📰 找到 {len(news_data)} 条最新新闻，正在快速处理...")

        # 简化处理：只基于标题生成总结，不获取内容
        summaries = []
        for i, news in enumerate(news_data):
            try:
                logger.info(f"正在处理第{i+1}条新闻: {news['title'][:30]}...")

                # 简化进度更新，减少频率
                if i % 2 == 0:  # 每2条更新一次
                    await processing_msg.edit_text(f"📰 正在处理第 {i+1}/{len(news_data)} 条新闻...")

                # 只基于标题生成总结，跳过内容获取
                summary = await crypto_scraper.summarize_news(news['title'])

                summaries.append({
                    'title': news['title'],
                    'summary': summary,
                    'source': news['source'],
                    'time': news['time']
                })

                logger.info(f"第{i+1}条新闻处理完成")

            except Exception as e:
                logger.error(f"处理第{i+1}条新闻失败: {e}")
                # 失败时添加基本信息
                summaries.append({
                    'title': news['title'],
                    'summary': f"📰 {news['title'][:50]}...",
                    'source': news['source'],
                    'time': news['time']
                })

        # 构建回复消息
        if summaries:
            # 删除处理消息
            await processing_msg.delete()

            # 发送标题
            header = f"📊 **最新加密货币新闻快讯**\n\n🔍 **找到 {len(news_data)} 条最新新闻**\n"
            await update.message.reply_text(header, parse_mode='Markdown')

            # 分批发送新闻，每批最多2条
            batch_size = 2
            for batch_start in range(0, len(summaries), batch_size):
                batch_end = min(batch_start + batch_size, len(summaries))
                batch_summaries = summaries[batch_start:batch_end]

                batch_text = ""
                for i, item in enumerate(batch_summaries):
                    news_index = batch_start + i + 1
                    title_display = item['title'][:30] + "..." if len(item['title']) > 30 else item['title']
                    summary_display = item['summary'][:80] + "..." if len(item['summary']) > 80 else item['summary']

                    batch_text += f"**{news_index}. {title_display}**\n"
                    batch_text += f"{summary_display}\n"
                    batch_text += f"📅 {item['source']} | {item['time']}\n\n"

                try:
                    await update.message.reply_text(batch_text, parse_mode='Markdown')
                except Exception as e:
                    logger.error(f"发送批次失败: {e}")
                    # 发送纯文本版本
                    await update.message.reply_text(batch_text)

                # 在批次之间稍作延迟
                await asyncio.sleep(0.3)

            # 发送时间戳
            footer = f"⏰ 更新时间: {datetime.now().strftime('%H:%M:%S')}"
            await update.message.reply_text(footer)
        else:
            await processing_msg.edit_text("😔 抱歉，无法生成新闻总结。")

    except Exception as e:
        logger.error(f"处理 /latest 命令失败: {e}")
        try:
            await update.message.reply_text("❌ 获取最新新闻时发生错误，请稍后再试。")
        except Exception as e2:
            logger.error(f"发送错误消息也失败: {e2}")

# /scrape 命令已删除

class TelegramBotServer:
    """Telegram Bot 服务器类"""

    def __init__(self):
        self.app_instance = None
        self.flask_app = None
        self.webhook_thread = None
        self.is_running = False

    def create_flask_app(self):
        """创建 Flask 应用"""
        app = Flask(__name__)

        @app.route("/webhook", methods=["POST"])
        def webhook():
            """处理 Telegram Webhook"""
            try:
                if not self.app_instance:
                    logger.error("Telegram Application 未初始化")
                    return Response(status=503)

                data = request.get_json(force=True)
                if not data:
                    logger.warning("收到空的 webhook 数据")
                    return Response(status=400)

                logger.debug(f"收到 webhook 数据: {json.dumps(data, indent=2)}")

                # 创建 Update 对象
                update = Update.de_json(data, self.app_instance.bot)
                if not update:
                    logger.warning("无法解析 Update 对象")
                    return Response(status=400)

                # 异步处理更新
                self._process_update_async(update)

                return Response(status=200)

            except Exception as e:
                logger.error(f"Webhook 处理失败: {e}", exc_info=True)
                return Response(status=500)

        @app.route("/health", methods=["GET"])
        def health_check():
            """健康检查端点"""
            try:
                status = {
                    "status": "ok",
                    "service": "telegram-bot",
                    "webhook_configured": bool(self.app_instance),
                    "timestamp": datetime.now().isoformat()
                }

                # 检查 webhook 状态
                if self.app_instance:
                    try:
                        # 这里可以添加更多健康检查逻辑
                        status["bot_username"] = self.app_instance.bot.username if hasattr(self.app_instance.bot, 'username') else "unknown"
                    except:
                        pass

                return jsonify(status)
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
                return jsonify({"status": "error", "message": str(e)}), 500

        @app.route("/webhook/info", methods=["GET"])
        def webhook_info():
            """获取 webhook 信息"""
            try:
                if not self.app_instance:
                    return jsonify({"error": "Bot not initialized"}), 503

                # 这里可以返回 webhook 配置信息
                return jsonify({
                    "webhook_url": WEBHOOK_URL,
                    "port": PORT,
                    "status": "configured"
                })
            except Exception as e:
                logger.error(f"获取 webhook 信息失败: {e}")
            return jsonify({"error": str(e)}), 500

        return app

    def _process_update_async(self, update):
        """异步处理 Telegram 更新"""
        def run_async():
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # 处理更新
                    loop.run_until_complete(self.app_instance.process_update(update))
                    logger.debug("Update 处理完成")
                except Exception as e:
                    logger.error(f"处理 Update 失败: {e}", exc_info=True)
                finally:
                    loop.close()

            except Exception as e:
                logger.error(f"异步处理失败: {e}", exc_info=True)

        # 在后台线程中运行
        thread = threading.Thread(target=run_async, daemon=True)
        thread.start()

    async def setup_webhook(self):
        """设置 Webhook"""
        try:
            # 删除旧的 webhook (如果存在)
            try:
                await self.app_instance.bot.delete_webhook()
                logger.info("旧的 Webhook 已删除。")
            except Exception as e:
                logger.warning(f"删除旧 Webhook 失败 (可能不存在): {e}")

            # 设置新的 webhook
            webhook_full_url = f"{WEBHOOK_URL.rstrip('/')}/webhook"
            await self.app_instance.bot.set_webhook(url=webhook_full_url)
            webhook_info = await self.app_instance.bot.get_webhook_info()
            logger.info(f"Webhook 已成功设置到: {webhook_full_url}")
            logger.info(f"Webhook 信息: {webhook_info}")

            if webhook_info.last_error_date:
                logger.warning(f"  Webhook 上次错误: {webhook_info.last_error_message}")

            return True

        except Exception as e:
            logger.error(f"Webhook 设置失败: {e}", exc_info=True)
            return False

    async def initialize_bot(self):
        """初始化 Telegram Bot"""
        try:
            # 创建 Application 实例
            self.app_instance = Application.builder().token(TELEGRAM_BOT_TOKEN).build()

            # 注册命令处理程序
            self.app_instance.add_handler(CommandHandler("start", start))
            self.app_instance.add_handler(CommandHandler("news", news_command))
            self.app_instance.add_handler(CommandHandler("about", about_command))
            self.app_instance.add_handler(CommandHandler("latest", latest_command))

            # 添加通用消息处理器来处理所有消息（包括首次交互）
            from telegram.ext import MessageHandler, filters
            self.app_instance.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_general_message))

            # 初始化 Application
            await self.app_instance.initialize()
            logger.info("Telegram Bot Application 初始化成功")

            # 设置Bot命令菜单
            await self.setup_bot_commands()

            return True

        except Exception as e:
            logger.error(f"Bot 初始化失败: {e}", exc_info=True)
            return False

    async def setup_bot_commands(self):
        """设置Bot命令菜单"""
        try:
            commands = [
                BotCommand("start", "开始使用Bot"),
                BotCommand("news", "获取最近24小时加密货币新闻"),
                BotCommand("latest", "获取最新实时新闻快讯"),
                BotCommand("about", "了解系统功能和使用方法")
            ]

            await self.app_instance.bot.set_my_commands(commands)
            logger.info("Bot命令菜单设置成功")

            # 尝试设置Bot的其他属性以允许直接命令
            try:
                # 获取Bot信息
                bot_info = await self.app_instance.bot.get_me()
                logger.info(f"Bot信息: {bot_info.username} (ID: {bot_info.id})")

                # 这里可以添加其他Bot配置

            except Exception as e:
                logger.warning(f"获取Bot信息失败: {e}")

        except Exception as e:
            logger.error(f"设置Bot命令菜单失败: {e}", exc_info=True)

    def start_server(self):
        """启动服务器"""
        try:
            self.is_running = True

            # 创建 Flask 应用
            self.flask_app = self.create_flask_app()

            logger.info(f"启动 Flask 服务器，监听 0.0.0.0:{PORT}")

            # 启动 Flask 服务器
            self.flask_app.run(
                host="0.0.0.0",
                port=PORT,
                debug=False,
                threaded=True,
                use_reloader=False
            )

        except Exception as e:
            logger.error(f"服务器启动失败: {e}", exc_info=True)
            raise

    async def shutdown(self):
        """关闭服务"""
        try:
            self.is_running = False

            if self.app_instance:
                logger.info("关闭 Telegram Bot...")
                # 删除 webhook
                try:
                    await self.app_instance.bot.delete_webhook()
                except:
                    pass

                # 关闭 Application
                await self.app_instance.shutdown()

            logger.info("服务关闭完成")

        except Exception as e:
            logger.error(f"关闭服务失败: {e}")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，准备关闭服务...")
    shutdown_event.set()

async def main_async():
    """异步主函数"""
    # 参数验证
    if not TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN 未设置。请在 .env 文件中配置。")
        return False

    if not WEBHOOK_URL:
        logger.error("WEBHOOK_URL 未设置。请在 .env 文件中配置。")
        return False

    # 创建服务器实例
    server = TelegramBotServer()

    try:
        # 初始化 Bot
        logger.info("初始化 Telegram Bot...")
        if not await server.initialize_bot():
            logger.error("Bot 初始化失败")
            return False

        # 设置 Webhook
        logger.info("设置 Webhook...")
        if not await server.setup_webhook():
            logger.error("Webhook 设置失败")
            return False

        logger.info("✅ Telegram Bot 初始化完成，准备启动服务器...")
        return server

    except Exception as e:
        logger.error(f"初始化失败: {e}", exc_info=True)
        await server.shutdown()
        return False

def main():
    """主函数"""
    logger.info("🚀 启动 Telegram Bot 服务...")

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 异步初始化
        server = asyncio.run(main_async())
        if not server:
            logger.error("❌ 服务初始化失败")
            sys.exit(1)

        # 启动 Flask 服务器（阻塞）
        logger.info(f"🌐 启动 Web 服务器，监听端口 {PORT}")
        server.start_server()

    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"服务运行失败: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("正在关闭服务...")
        try:
            if 'server' in locals():
                asyncio.run(server.shutdown())
        except:
            pass
        logger.info("👋 服务已关闭")

if __name__ == "__main__":
    main()
