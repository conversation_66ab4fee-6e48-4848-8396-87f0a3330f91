#!/bin/bash

# 新闻抓取守护进程启动脚本

cd /home/<USER>

echo "🚀 启动新闻抓取守护进程..."

# 检查是否已有运行的进程
if pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    echo "⚠️  检测到已运行的新闻抓取进程，正在停止..."
    pkill -f "python.*news_crawler_daemon.py"
    sleep 3
fi

# 检查是否已有news-crawler screen会话
if screen -list | grep -q "news-crawler"; then
    echo "⚠️  检测到已存在的screen会话，正在终止..."
    screen -S news-crawler -X quit
    sleep 2
fi

# 激活虚拟环境并启动守护进程
echo "📱 在screen会话中启动新闻抓取守护进程..."
screen -dmS news-crawler bash -c "
    cd /home/<USER>
    source venv/bin/activate
    export PYTHONPATH=/home/<USER>
    echo '🤖 新闻抓取守护进程正在启动...'
    python news_crawler_daemon.py
"

# 等待一下让进程启动
sleep 5

# 检查进程是否成功启动
if pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    echo "✅ 新闻抓取守护进程启动成功！"
    
    echo ""
    echo "📋 管理命令:"
    echo "  查看screen会话: screen -ls"
    echo "  连接到守护进程会话: screen -r news-crawler"
    echo "  分离screen会话: Ctrl+A, D"
    echo "  停止守护进程: screen -S news-crawler -X quit"
    echo ""
    echo "📊 守护进程配置:"
    echo "  抓取间隔: 5分钟"
    echo "  处理间隔: 10分钟"
    echo "  日志文件: /home/<USER>/news_crawler.log"
    echo ""
    echo "📝 查看日志:"
    echo "  实时日志: tail -f /home/<USER>/news_crawler.log"
    echo "  最近日志: tail -20 /home/<USER>/news_crawler.log"
    
else
    echo "❌ 新闻抓取守护进程启动失败！"
    echo "📋 故障排除:"
    echo "  查看screen会话: screen -r news-crawler"
    echo "  检查日志: tail -f /home/<USER>/news_crawler.log"
    exit 1
fi
