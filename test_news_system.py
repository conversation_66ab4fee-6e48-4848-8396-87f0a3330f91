import asyncio
import sys
import time
sys.path.append('/home/<USER>')

async def test_news_system():
    """测试完整的新闻系统"""
    print("🧪 测试完整的新闻系统...")
    print("=" * 60)
    
    # 1. 测试新闻抓取功能
    print("\n📡 步骤1: 测试新闻抓取功能...")
    try:
        from telegram_bot.main import CryptoPanicScraper
        scraper = CryptoPanicScraper()
        
        news_data = await scraper.scrape_latest_news(limit=5)
        if news_data:
            print(f"✅ 新闻抓取成功: {len(news_data)} 条")
            for i, news in enumerate(news_data[:3]):
                print(f"   {i+1}. {news['title'][:50]}...")
        else:
            print("❌ 新闻抓取失败")
            return False
    except Exception as e:
        print(f"❌ 新闻抓取异常: {e}")
        return False
    
    # 2. 测试数据库插入
    print("\n💾 步骤2: 测试数据库插入...")
    try:
        from utils.db_utils import insert_news
        
        # 测试插入第一条新闻
        test_news = news_data[0]
        if insert_news(test_news):
            print("✅ 数据库插入成功")
        else:
            print("❌ 数据库插入失败")
    except Exception as e:
        print(f"❌ 数据库插入异常: {e}")
    
    # 3. 测试数据库查询
    print("\n🔍 步骤3: 测试数据库查询...")
    try:
        from utils.db_utils import get_recent_news
        
        # 查询最近24小时新闻
        recent_news = get_recent_news(hours=24, limit=10)
        print(f"✅ 数据库查询成功: {len(recent_news)} 条最近新闻")
        
        # 查询特定币种新闻
        btc_news = get_recent_news(hours=24, coin='btc', limit=5)
        print(f"✅ BTC新闻查询: {len(btc_news)} 条")
        
    except Exception as e:
        print(f"❌ 数据库查询异常: {e}")
    
    # 4. 测试AI总结功能
    print("\n🤖 步骤4: 测试AI总结功能...")
    try:
        if news_data:
            test_news = news_data[0]
            content = await scraper.get_news_content(test_news['url'], timeout=10)
            summary = await scraper.summarize_news(test_news['title'], content)
            print(f"✅ AI总结成功: {summary[:60]}...")
        else:
            print("⚠️  跳过AI总结测试（无新闻数据）")
    except Exception as e:
        print(f"❌ AI总结异常: {e}")
    
    # 5. 测试守护进程状态
    print("\n🔄 步骤5: 检查守护进程状态...")
    import subprocess
    try:
        result = subprocess.run(['pgrep', '-f', 'news_crawler_daemon.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 新闻抓取守护进程: 运行中")
        else:
            print("❌ 新闻抓取守护进程: 未运行")
    except Exception as e:
        print(f"⚠️  守护进程检查异常: {e}")
    
    # 6. 测试Telegram Bot状态
    print("\n🤖 步骤6: 检查Telegram Bot状态...")
    try:
        import requests
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Telegram Bot: {data['status']} ({data['bot_username']})")
        else:
            print("❌ Telegram Bot: 健康检查失败")
    except Exception as e:
        print(f"❌ Telegram Bot检查异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 新闻系统测试完成！")
    
    # 7. 系统架构总结
    print("\n📋 系统架构总结:")
    print("✅ 新闻抓取: CryptoPanicScraper (实时抓取)")
    print("✅ 后台守护进程: news_crawler_daemon.py (定期抓取和处理)")
    print("✅ 数据存储: MySQL数据库 (持久化存储)")
    print("✅ AI总结: Gemini-2.0-Flash (智能总结)")
    print("✅ Telegram Bot: 用户交互界面")
    
    print("\n🔄 工作流程:")
    print("1. 守护进程每5分钟抓取最新新闻并保存到数据库")
    print("2. 守护进程每10分钟处理未处理的新闻（获取内容+AI总结）")
    print("3. 用户使用/news命令时，直接从数据库查询已处理的新闻")
    print("4. 用户使用/latest命令时，实时抓取并总结最新新闻")
    
    print("\n📊 优势:")
    print("• 响应速度快（数据库查询）")
    print("• 数据完整性好（持久化存储）")
    print("• 实时性强（定期更新+实时抓取）")
    print("• 智能化程度高（AI总结）")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_news_system())
    if success:
        print("\n🎊 新闻系统测试完成！系统已准备就绪！")
    else:
        print("\n❌ 部分测试失败，请检查配置")
