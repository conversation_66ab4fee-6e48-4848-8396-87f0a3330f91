#!/bin/bash

# Telegram Bot 停止脚本

echo "🛑 停止 Telegram Bot..."

# 停止screen会话
if screen -list | grep -q "tg-bot"; then
    echo "📱 终止screen会话..."
    screen -S tg-bot -X quit
    sleep 2
fi

# 强制停止Bot进程
if pgrep -f "python telegram_bot/main.py" > /dev/null; then
    echo "🔄 停止Bot进程..."
    pkill -f "python telegram_bot/main.py"
    sleep 3
fi

# 检查是否成功停止
if pgrep -f "python telegram_bot/main.py" > /dev/null; then
    echo "⚠️  强制终止Bot进程..."
    pkill -9 -f "python telegram_bot/main.py"
    sleep 2
fi

# 验证停止状态
if ! pgrep -f "python telegram_bot/main.py" > /dev/null; then
    echo "✅ Bot已成功停止"
else
    echo "❌ Bot停止失败"
    exit 1
fi
