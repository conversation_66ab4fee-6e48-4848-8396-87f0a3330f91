#!/bin/bash
# 手动修复命令菜单脚本

echo "🔧 手动修复Telegram Bot命令菜单..."

cd /home/<USER>
source venv/bin/activate

python3 -c "
import asyncio
import sys
sys.path.append('/home/<USER>')

async def manual_fix():
    from telegram_bot.main import TELEGRAM_BOT_TOKEN
    from telegram import Bo<PERSON>, BotCommand, BotCommandScopeDefault
    from utils.db_utils import get_all_users_from_db
    from telegram_bot.main import update_user_commands_for_activated_user
    
    bot = Bot(token=TELEGRAM_BOT_TOKEN)
    
    # 设置默认命令
    default_commands = [
        BotCommand('start', '开始使用Bot'),
        BotCommand('news', '获取最近24小时加密货币新闻'),
        BotCommand('latest', '获取最新实时新闻快讯'),
        BotCommand('about', '了解系统功能和使用方法')
    ]
    await bot.set_my_commands(default_commands, scope=BotCommandScopeDefault())
    print('✅ 默认命令菜单已设置')
    
    # 为所有用户更新命令菜单
    all_users = get_all_users_from_db()
    for user_id in all_users:
        try:
            await update_user_commands_for_activated_user(user_id, bot)
            print(f'✅ 用户 {user_id} 命令菜单已更新')
        except:
            pass
    
    print(f'✅ 完成！已为 {len(all_users)} 个用户更新命令菜单')

asyncio.run(manual_fix())
"

echo "✅ 命令菜单修复完成！"
