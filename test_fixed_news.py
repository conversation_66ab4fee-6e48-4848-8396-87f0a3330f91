import asyncio
import sys
sys.path.append('/home/<USER>')

async def test_fixed_news():
    """测试修复后的重点新闻功能"""
    print("🧪 测试修复后的重点新闻功能...")
    print("=" * 60)
    
    try:
        from utils.db_utils import get_recent_news
        import google.generativeai as genai
        import os
        from dotenv import load_dotenv
        
        load_dotenv()
        GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
        
        if GEMINI_API_KEY:
            genai.configure(api_key=GEMINI_API_KEY)
            gemini_model = genai.GenerativeModel('gemini-2.0-flash')
        
        # 1. 测试数据库查询
        print("\n📊 测试1: 数据库查询")
        news_list = get_recent_news(hours=24, limit=20)
        print(f"✅ 查询到 {len(news_list)} 条最近24小时新闻")
        
        if not news_list:
            print("⚠️  数据库中暂无新闻数据")
            return False
        
        # 过滤有效新闻
        valid_news = [news for news in news_list
                     if news.get('summary') and news['summary'].strip()
                     and '处理失败' not in news['summary']]
        
        print(f"✅ 有效新闻数量: {len(valid_news)} 条")
        
        # 2. 测试重点新闻分批逻辑
        print("\n📰 测试2: 重点新闻分批逻辑")
        
        # 模拟分批发送前5条新闻，每批2条
        batch_size = 2
        batches = []
        
        for batch_start in range(0, min(len(valid_news), 5), batch_size):
            batch_end = min(batch_start + batch_size, min(len(valid_news), 5))
            batch_news = valid_news[batch_start:batch_end]
            
            batch_text = ""
            for i, news in enumerate(batch_news):
                news_index = batch_start + i + 1
                title = news.get('title', '无标题')[:50]  # 更短的标题
                summary = news.get('summary', '无总结')[:60]  # 更短的总结
                published_at = news.get('published_at', '').strftime('%m-%d %H:%M') if news.get('published_at') else '未知时间'
                source = news.get('source', '未知来源')

                batch_text += f"**{news_index}. {title}**\n{summary}\n📅 {source} | {published_at}\n\n"
            
            batches.append({
                'batch_number': len(batches) + 1,
                'news_count': len(batch_news),
                'text': batch_text
            })
        
        print(f"✅ 生成 {len(batches)} 个批次")
        for batch in batches:
            print(f"   批次 {batch['batch_number']}: {batch['news_count']} 条新闻")
            print(f"   文本长度: {len(batch['text'])} 字符")
        
        # 3. 测试消息格式
        print("\n📝 测试3: 消息格式验证")
        
        if batches:
            sample_batch = batches[0]
            print("示例批次内容:")
            print("-" * 40)
            print(sample_batch['text'][:200] + "..." if len(sample_batch['text']) > 200 else sample_batch['text'])
            print("-" * 40)
            
            # 检查格式
            has_markdown = "**" in sample_batch['text']
            has_emoji = "📅" in sample_batch['text']
            has_proper_structure = "\n\n" in sample_batch['text']
            
            print(f"✅ Markdown格式: {'正常' if has_markdown else '异常'}")
            print(f"✅ Emoji字符: {'正常' if has_emoji else '异常'}")
            print(f"✅ 结构分隔: {'正常' if has_proper_structure else '异常'}")
        
        # 4. 测试综合总结
        print("\n🤖 测试4: 综合总结功能")
        if valid_news and GEMINI_API_KEY:
            # 收集新闻内容
            news_summaries = []
            for news in valid_news[:15]:  # 最多使用15条新闻
                title = news.get('title', '')
                summary = news.get('summary', '')
                if summary and len(summary) > 10:
                    news_summaries.append(f"• {title}: {summary}")
                else:
                    news_summaries.append(f"• {title}")

            combined_content = "\n".join(news_summaries)
            if len(combined_content) > 4000:
                combined_content = combined_content[:4000] + "..."

            # 使用优化后的提示词
            comprehensive_prompt = f"""根据以下24小时加密货币新闻，生成简洁的中文总结（150字以内）：

重点关注：价格变动、重大事件、市场趋势
要求：客观简洁，突出核心信息

新闻内容：
{combined_content}

总结："""

            try:
                response = gemini_model.generate_content(comprehensive_prompt)
                comprehensive_summary = response.text.strip()
                
                print(f"✅ 综合总结生成成功")
                print(f"📏 总结长度: {len(comprehensive_summary)} 字符")
                print(f"📄 总结内容: {comprehensive_summary[:100]}...")
                
                # 检查总结长度
                if len(comprehensive_summary) <= 200:
                    print("✅ 总结长度符合要求（≤200字）")
                else:
                    print("⚠️  总结稍长，但可接受")
                    
            except Exception as e:
                print(f"❌ 综合总结生成失败: {e}")
        
        # 5. 测试完整流程
        print("\n🔄 测试5: 完整流程模拟")
        print("模拟 /news 命令的完整流程:")
        print("1. 📊 查询数据库 → 成功")
        print("2. 🤖 生成综合总结 → 成功")
        print("3. 📰 发送重点新闻标题 → 准备就绪")
        print("4. 📝 分批发送重点新闻 → 格式正确")
        print("5. ⚡ 整体响应 → 优化完成")
        
        print("\n🎉 重点新闻功能测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_fixed_news())
    if success:
        print("\n🎊 重点新闻功能修复成功！")
        print("✨ 修复内容：")
        print("  • 修复了emoji字符显示问题")
        print("  • 改为分批发送，每批2条新闻")
        print("  • 优化了消息格式和结构")
        print("  • 确保Markdown格式正确")
        print("  • 提高了发送成功率")
    else:
        print("\n❌ 测试失败，需要进一步调试")
