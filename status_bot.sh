#!/bin/bash

# Telegram Bot 状态检查脚本

echo "📊 Telegram Bot 状态检查"
echo "=========================="

# 检查Bot进程
if pgrep -f "python telegram_bot/main.py" > /dev/null; then
    PID=$(pgrep -f "python telegram_bot/main.py")
    echo "✅ Bot进程: 运行中 (PID: $PID)"
    
    # 显示进程信息
    echo "📋 进程信息:"
    ps aux | grep "python telegram_bot/main.py" | grep -v grep | while read line; do
        echo "   $line"
    done
else
    echo "❌ Bot进程: 未运行"
fi

echo ""

# 检查screen会话
echo "📱 Screen会话:"
if screen -list | grep -q "tg-bot"; then
    echo "✅ tg-bot会话: 存在"
    screen -list | grep "tg-bot"
else
    echo "❌ tg-bot会话: 不存在"
fi

echo ""

# 检查端口占用
echo "🌐 端口状态:"
if netstat -tlnp 2>/dev/null | grep ":5000" > /dev/null; then
    echo "✅ 端口5000: 已占用"
    netstat -tlnp 2>/dev/null | grep ":5000"
else
    echo "❌ 端口5000: 未占用"
fi

echo ""

# 检查Bot健康状态
echo "🔍 Bot健康检查:"
if curl -s http://localhost:5000/health > /dev/null; then
    echo "✅ 健康检查: 通过"
    echo "📊 Bot信息:"
    curl -s http://localhost:5000/health | python3 -m json.tool 2>/dev/null || echo "   无法解析JSON响应"
else
    echo "❌ 健康检查: 失败"
fi

echo ""

# 检查Webhook信息
echo "🔗 Webhook状态:"
if curl -s http://localhost:5000/webhook/info > /dev/null; then
    echo "✅ Webhook端点: 可访问"
    echo "📋 Webhook信息:"
    curl -s http://localhost:5000/webhook/info | python3 -m json.tool 2>/dev/null || echo "   无法解析JSON响应"
else
    echo "❌ Webhook端点: 不可访问"
fi

echo ""
echo "=========================="
echo "📋 管理命令:"
echo "  启动Bot: ./start_bot.sh"
echo "  停止Bot: ./stop_bot.sh"
echo "  查看状态: ./status_bot.sh"
echo "  连接screen: screen -r tg-bot"
echo "  查看日志: tail -f bot.log"
