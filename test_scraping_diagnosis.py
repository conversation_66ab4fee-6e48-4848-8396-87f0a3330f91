import asyncio
import sys
sys.path.append('/home/<USER>')

async def test_scraping_diagnosis():
    """诊断新闻抓取问题"""
    print("🔍 诊断新闻抓取问题...")
    print("=" * 60)
    
    try:
        from telegram_bot.main import crypto_scraper
        from playwright.async_api import async_playwright
        
        # 1. 测试基本抓取功能
        print("\n📡 测试1: 基本抓取功能")
        try:
            news_data = await crypto_scraper.scrape_latest_news(limit=10)
            print(f"✅ 抓取结果: {len(news_data)} 条新闻")
            
            if news_data:
                print("前3条新闻:")
                for i, news in enumerate(news_data[:3]):
                    print(f"  {i+1}. {news['title'][:50]}...")
                    print(f"     来源: {news['source']}")
                    print(f"     时间: {news['time']}")
                    print(f"     URL: {news['url'][:60]}...")
            else:
                print("❌ 没有抓取到任何新闻")
                
        except Exception as e:
            print(f"❌ 抓取失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 2. 手动测试网站访问
        print("\n🌐 测试2: 手动网站访问")
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                print("正在访问 CryptoPanic...")
                await page.goto("https://cryptopanic.com")
                await page.wait_for_load_state('networkidle', timeout=30000)
                
                # 检查页面标题
                title = await page.title()
                print(f"✅ 页面标题: {title}")
                
                # 检查新闻项目选择器
                selectors_to_test = [
                    '.news-row',
                    '.news-item',
                    '[data-testid="news-item"]',
                    '.post',
                    '.article',
                    'article'
                ]
                
                for selector in selectors_to_test:
                    try:
                        elements = await page.query_selector_all(selector)
                        print(f"✅ 选择器 '{selector}': 找到 {len(elements)} 个元素")
                        if len(elements) > 0:
                            break
                    except:
                        print(f"❌ 选择器 '{selector}': 失败")
                
                # 获取页面内容片段
                content = await page.inner_text('body')
                print(f"✅ 页面内容长度: {len(content)} 字符")
                
                # 检查是否包含新闻相关内容
                if 'bitcoin' in content.lower() or 'crypto' in content.lower():
                    print("✅ 页面包含加密货币相关内容")
                else:
                    print("⚠️  页面可能不包含预期的加密货币内容")
                
                await browser.close()
                
        except Exception as e:
            print(f"❌ 手动访问失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 3. 测试不同的抓取策略
        print("\n🔧 测试3: 不同的抓取策略")
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # 设置用户代理
                await page.set_extra_http_headers({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                })
                
                await page.goto("https://cryptopanic.com")
                await page.wait_for_load_state('networkidle')
                
                # 尝试不同的选择器策略
                strategies = [
                    {
                        'name': '原始策略',
                        'selector': '.news-row',
                        'link_selector': 'a[href*="/news/"]'
                    },
                    {
                        'name': '通用链接策略',
                        'selector': 'div',
                        'link_selector': 'a[href*="news"]'
                    },
                    {
                        'name': '文章策略',
                        'selector': 'article',
                        'link_selector': 'a'
                    }
                ]
                
                for strategy in strategies:
                    try:
                        print(f"尝试策略: {strategy['name']}")
                        
                        # 查找容器元素
                        containers = await page.query_selector_all(strategy['selector'])
                        print(f"  找到 {len(containers)} 个容器")
                        
                        news_found = 0
                        for i, container in enumerate(containers[:10]):  # 只检查前10个
                            try:
                                # 查找链接
                                link = await container.query_selector(strategy['link_selector'])
                                if link:
                                    href = await link.get_attribute('href')
                                    text = await link.inner_text()
                                    if href and text and len(text.strip()) > 10:
                                        news_found += 1
                                        if news_found <= 3:  # 只显示前3个
                                            print(f"    {news_found}. {text.strip()[:50]}...")
                            except:
                                continue
                        
                        print(f"  策略结果: 找到 {news_found} 条可能的新闻")
                        
                        if news_found >= 5:
                            print(f"✅ 策略 '{strategy['name']}' 效果良好")
                            break
                        else:
                            print(f"⚠️  策略 '{strategy['name']}' 效果一般")
                            
                    except Exception as e:
                        print(f"❌ 策略 '{strategy['name']}' 失败: {e}")
                
                await browser.close()
                
        except Exception as e:
            print(f"❌ 策略测试失败: {e}")
        
        # 4. 检查网络和配置
        print("\n⚙️  测试4: 检查网络和配置")
        
        # 检查环境变量
        import os
        from dotenv import load_dotenv
        load_dotenv()
        
        gemini_key = os.getenv("GEMINI_API_KEY")
        print(f"✅ GEMINI_API_KEY: {'已设置' if gemini_key else '未设置'}")
        
        # 检查网络连接
        try:
            import requests
            response = requests.get("https://cryptopanic.com", timeout=10)
            print(f"✅ 网络连接: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 网络连接失败: {e}")
        
        print("\n🎉 诊断完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_scraping_diagnosis())
    if success:
        print("\n📋 诊断总结:")
        print("• 如果抓取到的新闻少于预期，可能是网站结构变化")
        print("• 如果网络连接正常但抓取失败，可能需要更新选择器")
        print("• 如果页面访问失败，可能是反爬虫机制")
        print("• 建议根据诊断结果调整抓取策略")
    else:
        print("\n❌ 诊断失败，需要进一步检查")
