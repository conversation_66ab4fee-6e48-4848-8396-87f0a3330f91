#!/bin/bash

# 全系统状态检查脚本

echo "🚀 nyxn-ai 新闻系统全状态检查"
echo "=============================================="

# 1. 检查Screen会话
echo ""
echo "📱 Screen会话状态:"
if screen -list | grep -q "tg-bot"; then
    echo "✅ tg-bot会话: 存在"
else
    echo "❌ tg-bot会话: 不存在"
fi

if screen -list | grep -q "news-crawler"; then
    echo "✅ news-crawler会话: 存在"
else
    echo "❌ news-crawler会话: 不存在"
fi

# 2. 检查进程状态
echo ""
echo "🔄 进程状态:"
if pgrep -f "python telegram_bot/main.py" > /dev/null; then
    PID=$(pgrep -f "python telegram_bot/main.py")
    echo "✅ Telegram Bot: 运行中 (PID: $PID)"
else
    echo "❌ Telegram Bot: 未运行"
fi

if pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    PID=$(pgrep -f "python.*news_crawler_daemon.py")
    echo "✅ 新闻抓取守护进程: 运行中 (PID: $PID)"
else
    echo "❌ 新闻抓取守护进程: 未运行"
fi

# 3. 检查端口占用
echo ""
echo "🌐 端口状态:"
if netstat -tlnp 2>/dev/null | grep ":5000" > /dev/null; then
    echo "✅ 端口5000: 已占用 (Telegram Bot)"
else
    echo "❌ 端口5000: 未占用"
fi

# 4. 检查Bot健康状态
echo ""
echo "🤖 Telegram Bot健康检查:"
if curl -s http://localhost:5000/health > /dev/null; then
    echo "✅ 健康检查: 通过"
    echo "📊 Bot信息:"
    curl -s http://localhost:5000/health | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(f'   状态: {data[\"status\"]}')
    print(f'   用户名: {data[\"bot_username\"]}')
    print(f'   Webhook: {data[\"webhook_configured\"]}')
    print(f'   时间戳: {data[\"timestamp\"]}')
except:
    print('   无法解析响应')
" 2>/dev/null
else
    echo "❌ 健康检查: 失败"
fi

# 5. 检查数据库状态
echo ""
echo "📊 数据库统计:"
cd /home/<USER>
source venv/bin/activate 2>/dev/null

python3 -c "
import sys
sys.path.append('/home/<USER>')
try:
    from utils.db_utils import get_db_cursor
    with get_db_cursor() as (cursor, conn):
        # 总新闻数
        cursor.execute('SELECT COUNT(*) as total FROM news')
        total = cursor.fetchone()['total']
        print(f'📰 总新闻数: {total}')
        
        # 最近24小时新闻数
        cursor.execute('SELECT COUNT(*) as recent FROM news WHERE created_at >= NOW() - INTERVAL 24 HOUR')
        recent = cursor.fetchone()['recent']
        print(f'🕐 最近24小时: {recent}')
        
        # 已处理新闻数
        cursor.execute('SELECT COUNT(*) as processed FROM news WHERE summary IS NOT NULL AND summary != \"\"')
        processed = cursor.fetchone()['processed']
        print(f'✅ 已处理: {processed}')
        
        # 未处理新闻数
        cursor.execute('SELECT COUNT(*) as unprocessed FROM news WHERE summary IS NULL OR summary = \"\"')
        unprocessed = cursor.fetchone()['unprocessed']
        print(f'⏳ 未处理: {unprocessed}')
        
        # 最新新闻时间
        cursor.execute('SELECT MAX(created_at) as latest FROM news')
        latest = cursor.fetchone()['latest']
        if latest:
            print(f'🕒 最新新闻: {latest}')
        
except Exception as e:
    print(f'❌ 数据库查询失败: {e}')
" 2>/dev/null

# 6. 检查日志文件
echo ""
echo "📝 日志文件状态:"
if [ -f "/home/<USER>/news_crawler.log" ]; then
    echo "✅ 新闻抓取日志: 存在"
    echo "📏 文件大小: $(du -h /home/<USER>/news_crawler.log | cut -f1)"
else
    echo "❌ 新闻抓取日志: 不存在"
fi

# 7. 系统资源使用
echo ""
echo "💻 系统资源:"
echo "🖥️  CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "💾 内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2 }')"
echo "💿 磁盘使用: $(df -h / | awk 'NR==2{print $5}')"

echo ""
echo "=============================================="
echo "📋 快速管理命令:"
echo "  启动全部服务: ./start_all.sh"
echo "  停止全部服务: ./stop_all.sh"
echo "  重启全部服务: ./restart_all.sh"
echo "  查看全状态: ./status_all.sh"
echo ""
echo "  Telegram Bot:"
echo "    启动: ./start_bot.sh"
echo "    停止: ./stop_bot.sh"
echo "    连接: screen -r tg-bot"
echo ""
echo "  新闻抓取守护进程:"
echo "    启动: ./start_news_crawler.sh"
echo "    停止: ./stop_news_crawler.sh"
echo "    连接: screen -r news-crawler"
echo "    日志: tail -f news_crawler.log"
echo ""
echo "🌐 服务端点:"
echo "  健康检查: http://localhost:5000/health"
echo "  Webhook: https://hook.nyxn.ai/webhook"
