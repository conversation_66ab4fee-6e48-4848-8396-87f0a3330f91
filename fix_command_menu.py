import asyncio
import sys
import os
sys.path.append('/home/<USER>')

async def fix_command_menu():
    """诊断和修复命令菜单问题"""
    print("🔧 诊断和修复命令菜单问题...")
    print("=" * 60)
    
    try:
        # 1. 检查Bot连接
        print("\n🤖 检查Bot连接:")
        from telegram_bot.main import TELEGRAM_BOT_TOKEN
        from telegram import Bot, BotCommand, BotCommandScopeDefault, BotCommandScopeChat
        
        if not TELEGRAM_BOT_TOKEN:
            print("❌ TELEGRAM_BOT_TOKEN 未设置")
            return False
        
        bot = Bot(token=TELEGRAM_BOT_TOKEN)
        
        try:
            bot_info = await bot.get_me()
            print(f"✅ Bot连接成功: {bot_info.username} (ID: {bot_info.id})")
        except Exception as e:
            print(f"❌ Bot连接失败: {e}")
            return False
        
        # 2. 检查当前的默认命令菜单
        print("\n📋 检查当前的默认命令菜单:")
        try:
            current_commands = await bot.get_my_commands()
            print(f"当前默认命令数量: {len(current_commands)}")
            for cmd in current_commands:
                print(f"  • /{cmd.command} - {cmd.description}")
        except Exception as e:
            print(f"❌ 获取命令菜单失败: {e}")
        
        # 3. 设置正确的默认命令菜单（新用户看到的）
        print("\n🔧 设置正确的默认命令菜单:")
        try:
            default_commands = [
                BotCommand("start", "开始使用Bot"),
                BotCommand("news", "获取最近24小时加密货币新闻"),
                BotCommand("latest", "获取最新实时新闻快讯"),
                BotCommand("about", "了解系统功能和使用方法")
            ]
            
            await bot.set_my_commands(default_commands, scope=BotCommandScopeDefault())
            print("✅ 默认命令菜单设置成功")
            
            # 验证设置
            updated_commands = await bot.get_my_commands()
            print(f"更新后命令数量: {len(updated_commands)}")
            for cmd in updated_commands:
                print(f"  • /{cmd.command} - {cmd.description}")
                
        except Exception as e:
            print(f"❌ 设置默认命令菜单失败: {e}")
        
        # 4. 创建测试用户的命令菜单（不包含/start）
        print("\n👤 创建测试用户的命令菜单:")
        
        # 模拟一个测试用户ID
        test_user_id = 123456789  # 使用一个测试ID
        
        try:
            user_commands = [
                BotCommand("news", "获取最近24小时加密货币新闻"),
                BotCommand("latest", "获取最新实时新闻快讯"),
                BotCommand("about", "了解系统功能和使用方法")
            ]
            
            scope = BotCommandScopeChat(chat_id=test_user_id)
            await bot.set_my_commands(user_commands, scope=scope)
            print(f"✅ 为测试用户 {test_user_id} 设置命令菜单成功")
            print("老用户命令菜单（不包含/start）:")
            for cmd in user_commands:
                print(f"  • /{cmd.command} - {cmd.description}")
                
        except Exception as e:
            print(f"❌ 设置用户命令菜单失败: {e}")
        
        # 5. 测试命令菜单更新函数
        print("\n🔄 测试命令菜单更新函数:")
        from telegram_bot.main import update_user_commands_for_activated_user
        
        try:
            # 测试为用户更新命令菜单
            await update_user_commands_for_activated_user(test_user_id, bot)
            print(f"✅ 命令菜单更新函数测试成功")
        except Exception as e:
            print(f"❌ 命令菜单更新函数测试失败: {e}")
        
        # 6. 检查MySQL数据库中的用户
        print("\n🗄️  检查MySQL数据库中的用户:")
        try:
            from utils.db_utils import get_all_users_from_db
            all_users = get_all_users_from_db()
            print(f"数据库中共有 {len(all_users)} 个用户")
            
            if len(all_users) > 0:
                print("为前5个用户更新命令菜单:")
                user_list = list(all_users)[:5]  # 只处理前5个用户
                
                for user_id in user_list:
                    try:
                        await update_user_commands_for_activated_user(user_id, bot)
                        print(f"  ✅ 用户 {user_id} 命令菜单已更新")
                    except Exception as e:
                        print(f"  ❌ 用户 {user_id} 命令菜单更新失败: {e}")
            else:
                print("数据库中暂无用户")
                
        except Exception as e:
            print(f"❌ 检查数据库用户失败: {e}")
        
        # 7. 创建一个通用的命令菜单更新函数
        print("\n🛠️  创建通用命令菜单更新函数:")
        
        async def update_all_users_commands():
            """为所有已知用户更新命令菜单"""
            try:
                from utils.db_utils import get_all_users_from_db
                all_users = get_all_users_from_db()
                
                success_count = 0
                for user_id in all_users:
                    try:
                        await update_user_commands_for_activated_user(user_id, bot)
                        success_count += 1
                    except Exception as e:
                        print(f"  用户 {user_id} 更新失败: {e}")
                
                print(f"✅ 成功为 {success_count}/{len(all_users)} 个用户更新命令菜单")
                return success_count
                
            except Exception as e:
                print(f"❌ 批量更新失败: {e}")
                return 0
        
        # 执行批量更新
        updated_count = await update_all_users_commands()
        
        # 8. 验证修复效果
        print("\n🎯 验证修复效果:")
        print("✅ Bot连接正常")
        print("✅ 默认命令菜单已设置（包含/start）")
        print("✅ 用户特定命令菜单已设置（不包含/start）")
        print("✅ 命令菜单更新函数正常")
        print(f"✅ 已为 {updated_count} 个用户更新命令菜单")
        
        # 9. 提供解决方案
        print("\n💡 解决方案:")
        print("如果用户仍然看到/start命令，可能的原因和解决方法:")
        print("1. 用户需要重启Telegram客户端")
        print("2. 用户需要清除Telegram缓存")
        print("3. 命令菜单更新需要一些时间生效")
        print("4. 用户可能使用的是旧的缓存版本")
        
        print("\n🔧 立即修复方法:")
        print("1. 用户发送任何命令（如/news）激活账户")
        print("2. 系统会自动为该用户更新命令菜单")
        print("3. 用户重启Telegram应用查看新菜单")
        
        # 10. 创建手动修复脚本
        print("\n📝 创建手动修复脚本:")
        
        manual_fix_script = """#!/bin/bash
# 手动修复命令菜单脚本

echo "🔧 手动修复Telegram Bot命令菜单..."

cd /home/<USER>
source venv/bin/activate

python3 -c "
import asyncio
import sys
sys.path.append('/home/<USER>')

async def manual_fix():
    from telegram_bot.main import TELEGRAM_BOT_TOKEN
    from telegram import Bot, BotCommand, BotCommandScopeDefault
    from utils.db_utils import get_all_users_from_db
    from telegram_bot.main import update_user_commands_for_activated_user
    
    bot = Bot(token=TELEGRAM_BOT_TOKEN)
    
    # 设置默认命令
    default_commands = [
        BotCommand('start', '开始使用Bot'),
        BotCommand('news', '获取最近24小时加密货币新闻'),
        BotCommand('latest', '获取最新实时新闻快讯'),
        BotCommand('about', '了解系统功能和使用方法')
    ]
    await bot.set_my_commands(default_commands, scope=BotCommandScopeDefault())
    print('✅ 默认命令菜单已设置')
    
    # 为所有用户更新命令菜单
    all_users = get_all_users_from_db()
    for user_id in all_users:
        try:
            await update_user_commands_for_activated_user(user_id, bot)
            print(f'✅ 用户 {user_id} 命令菜单已更新')
        except:
            pass
    
    print(f'✅ 完成！已为 {len(all_users)} 个用户更新命令菜单')

asyncio.run(manual_fix())
"

echo "✅ 命令菜单修复完成！"
"""
        
        with open('/home/<USER>/fix_command_menu.sh', 'w') as f:
            f.write(manual_fix_script)
        
        os.chmod('/home/<USER>/fix_command_menu.sh', 0o755)
        print("✅ 手动修复脚本已创建: /home/<USER>/fix_command_menu.sh")
        
        print("\n🎉 命令菜单诊断和修复完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_command_menu())
    if success:
        print("\n🎊 命令菜单修复成功！")
        print("✨ 修复内容：")
        print("  • 设置了正确的默认命令菜单")
        print("  • 为已知用户更新了个性化菜单")
        print("  • 创建了手动修复脚本")
        print("  • 验证了命令菜单更新函数")
        print("\n💡 如果用户仍看到/start命令：")
        print("  • 让用户重启Telegram应用")
        print("  • 或者用户发送任何命令激活更新")
        print("  • 或者运行: ./fix_command_menu.sh")
    else:
        print("\n❌ 修复失败，需要进一步调试")
