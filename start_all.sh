#!/bin/bash

# 一键启动全部服务脚本

echo "🚀 启动 nyxn-ai 新闻系统全部服务..."
echo "=============================================="

cd /home/<USER>

# 1. 启动新闻抓取守护进程
echo ""
echo "📡 启动新闻抓取守护进程..."
./start_news_crawler.sh

echo ""
echo "⏳ 等待守护进程完全启动..."
sleep 3

# 2. 启动Telegram Bot
echo ""
echo "🤖 启动Telegram Bot..."
./start_bot.sh

echo ""
echo "⏳ 等待Bot完全启动..."
sleep 2

# 3. 验证所有服务状态
echo ""
echo "🔍 验证服务状态..."
echo "=============================================="

# 检查进程
if pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    echo "✅ 新闻抓取守护进程: 启动成功"
else
    echo "❌ 新闻抓取守护进程: 启动失败"
fi

if pgrep -f "python telegram_bot/main.py" > /dev/null; then
    echo "✅ Telegram Bot: 启动成功"
else
    echo "❌ Telegram Bot: 启动失败"
fi

# 检查健康状态
if curl -s http://localhost:5000/health > /dev/null; then
    echo "✅ Bot健康检查: 通过"
else
    echo "❌ Bot健康检查: 失败"
fi

echo ""
echo "🎉 全部服务启动完成！"
echo ""
echo "📋 管理命令:"
echo "  查看全状态: ./status_all.sh"
echo "  停止全部服务: ./stop_all.sh"
echo "  重启全部服务: ./restart_all.sh"
echo ""
echo "📱 Screen会话:"
echo "  连接Bot: screen -r tg-bot"
echo "  连接守护进程: screen -r news-crawler"
echo ""
echo "🌐 服务端点:"
echo "  健康检查: http://localhost:5000/health"
echo "  Webhook: https://hook.nyxn.ai/webhook"
