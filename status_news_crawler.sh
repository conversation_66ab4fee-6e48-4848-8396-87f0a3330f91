#!/bin/bash

# 新闻抓取守护进程状态检查脚本

echo "📊 新闻抓取守护进程状态检查"
echo "=================================="

# 检查守护进程
if pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    PID=$(pgrep -f "python.*news_crawler_daemon.py")
    echo "✅ 守护进程: 运行中 (PID: $PID)"
    
    # 显示进程信息
    echo "📋 进程信息:"
    ps aux | grep "python.*news_crawler_daemon.py" | grep -v grep | while read line; do
        echo "   $line"
    done
else
    echo "❌ 守护进程: 未运行"
fi

echo ""

# 检查screen会话
echo "📱 Screen会话:"
if screen -list | grep -q "news-crawler"; then
    echo "✅ news-crawler会话: 存在"
    screen -list | grep "news-crawler"
else
    echo "❌ news-crawler会话: 不存在"
fi

echo ""

# 检查日志文件
echo "📝 日志文件状态:"
if [ -f "/home/<USER>/news_crawler.log" ]; then
    echo "✅ 日志文件: 存在"
    echo "📏 文件大小: $(du -h /home/<USER>/news_crawler.log | cut -f1)"
    echo "🕐 最后修改: $(stat -c %y /home/<USER>/news_crawler.log)"
    
    echo ""
    echo "📄 最近5行日志:"
    tail -5 /home/<USER>/news_crawler.log | while read line; do
        echo "   $line"
    done
else
    echo "❌ 日志文件: 不存在"
fi

echo ""

# 检查数据库中的新闻数量
echo "📊 数据库统计:"
cd /home/<USER>
source venv/bin/activate 2>/dev/null

python3 -c "
import sys
sys.path.append('/home/<USER>')
try:
    from utils.db_utils import get_db_cursor
    with get_db_cursor() as (cursor, conn):
        # 总新闻数
        cursor.execute('SELECT COUNT(*) as total FROM news')
        total = cursor.fetchone()['total']
        print(f'📰 总新闻数: {total}')
        
        # 最近24小时新闻数
        cursor.execute('SELECT COUNT(*) as recent FROM news WHERE created_at >= NOW() - INTERVAL 24 HOUR')
        recent = cursor.fetchone()['recent']
        print(f'🕐 最近24小时: {recent}')
        
        # 已处理新闻数
        cursor.execute('SELECT COUNT(*) as processed FROM news WHERE summary IS NOT NULL AND summary != \"\"')
        processed = cursor.fetchone()['processed']
        print(f'✅ 已处理: {processed}')
        
        # 未处理新闻数
        cursor.execute('SELECT COUNT(*) as unprocessed FROM news WHERE summary IS NULL OR summary = \"\"')
        unprocessed = cursor.fetchone()['unprocessed']
        print(f'⏳ 未处理: {unprocessed}')
        
except Exception as e:
    print(f'❌ 数据库查询失败: {e}')
" 2>/dev/null

echo ""
echo "=================================="
echo "📋 管理命令:"
echo "  启动守护进程: ./start_news_crawler.sh"
echo "  停止守护进程: ./stop_news_crawler.sh"
echo "  查看状态: ./status_news_crawler.sh"
echo "  连接screen: screen -r news-crawler"
echo "  查看日志: tail -f news_crawler.log"
