#!/usr/bin/env python3
"""
新闻抓取守护进程
定期抓取最新新闻并保存到数据库，同时处理未处理的新闻内容和总结
"""

import os
import sys
import time
import asyncio
import logging
import signal
import threading
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 添加项目路径
sys.path.append('/home/<USER>')

from telegram_bot.main import CryptoPanicScraper
from utils.db_utils import insert_news, get_unprocessed_news_urls, update_news_content
import google.generativeai as genai

# 加载环境变量
load_dotenv(override=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/news_crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
CRAWL_INTERVAL = 300  # 5分钟抓取一次新闻
PROCESS_INTERVAL = 600  # 10分钟处理一次未处理的新闻
MAX_NEWS_PER_CRAWL = 20  # 每次抓取的最大新闻数
MAX_PROCESS_PER_BATCH = 10  # 每批处理的最大新闻数

# 初始化 Gemini
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    gemini_model = genai.GenerativeModel('gemini-2.0-flash')
else:
    logger.error("GEMINI_API_KEY 未设置")
    sys.exit(1)

# 全局变量
running = True
scraper = None

class NewsCrawlerDaemon:
    """新闻抓取守护进程"""
    
    def __init__(self):
        self.scraper = CryptoPanicScraper()
        self.last_crawl_time = None
        self.last_process_time = None
        
    async def crawl_latest_news(self):
        """抓取最新新闻并保存到数据库"""
        try:
            logger.info("开始抓取最新新闻...")
            
            # 抓取新闻
            news_data = await self.scraper.scrape_latest_news(limit=MAX_NEWS_PER_CRAWL)
            
            if not news_data:
                logger.warning("未抓取到新闻")
                return 0
            
            # 保存到数据库
            saved_count = 0
            for news_item in news_data:
                try:
                    if insert_news(news_item):
                        saved_count += 1
                except Exception as e:
                    logger.error(f"保存新闻失败: {e}")
            
            logger.info(f"抓取完成: 获取 {len(news_data)} 条，保存 {saved_count} 条")
            self.last_crawl_time = datetime.now()
            return saved_count
            
        except Exception as e:
            logger.error(f"抓取新闻失败: {e}")
            return 0
    
    async def process_unprocessed_news(self):
        """处理未处理的新闻（获取内容和生成总结）"""
        try:
            logger.info("开始处理未处理的新闻...")
            
            # 获取未处理的新闻
            unprocessed_news = get_unprocessed_news_urls(limit=MAX_PROCESS_PER_BATCH)
            
            if not unprocessed_news:
                logger.info("没有需要处理的新闻")
                return 0
            
            processed_count = 0
            for news in unprocessed_news:
                try:
                    logger.info(f"处理新闻 ID {news['id']}: {news['title'][:50]}...")
                    
                    # 获取新闻内容
                    content = await self.scraper.get_news_content(news['url'], timeout=15)
                    
                    # 生成总结
                    summary = await self.generate_summary(news['title'], content)
                    
                    # 更新数据库
                    if update_news_content(news['id'], content or "", summary):
                        processed_count += 1
                        logger.info(f"新闻 ID {news['id']} 处理完成")
                    
                    # 避免请求过快
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.error(f"处理新闻 ID {news['id']} 失败: {e}")
                    # 即使失败也保存一个基本总结
                    try:
                        basic_summary = await self.generate_summary(news['title'])
                        update_news_content(news['id'], "", basic_summary)
                    except:
                        pass
            
            logger.info(f"处理完成: 处理 {processed_count}/{len(unprocessed_news)} 条新闻")
            self.last_process_time = datetime.now()
            return processed_count
            
        except Exception as e:
            logger.error(f"处理未处理新闻失败: {e}")
            return 0
    
    async def generate_summary(self, title, content=None):
        """生成新闻总结"""
        try:
            if content and len(content) > 50:
                # 有内容时的总结
                prompt = f"""请总结以下加密货币新闻内容，生成一个简洁的中文简讯。

要求：
1. 重点突出加密货币相关信息
2. 保持客观中性的语调
3. 简讯长度控制在150字以内

新闻标题：{title}

新闻内容：
{content[:2000]}

简讯总结："""
            else:
                # 仅基于标题的总结
                prompt = f"""请基于以下新闻标题，生成一个简洁的中文简讯。

要求：
1. 重点突出加密货币相关信息
2. 保持客观中性的语调
3. 简讯长度控制在100字以内
4. 如果标题信息不足，请说明"需要更多信息"

新闻标题：{title}

简讯总结："""

            response = gemini_model.generate_content(prompt)
            summary = response.text.strip()
            return f"📝 {summary}"

        except Exception as e:
            logger.error(f"AI总结失败: {e}")
            return f"📰 {title[:100]}..."
    
    async def run_crawl_cycle(self):
        """运行抓取周期"""
        while running:
            try:
                await self.crawl_latest_news()
                
                # 等待下次抓取
                await asyncio.sleep(CRAWL_INTERVAL)
                
            except Exception as e:
                logger.error(f"抓取周期异常: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟
    
    async def run_process_cycle(self):
        """运行处理周期"""
        while running:
            try:
                await self.process_unprocessed_news()
                
                # 等待下次处理
                await asyncio.sleep(PROCESS_INTERVAL)
                
            except Exception as e:
                logger.error(f"处理周期异常: {e}")
                await asyncio.sleep(120)  # 出错时等待2分钟
    
    async def run(self):
        """运行守护进程"""
        logger.info("🚀 新闻抓取守护进程启动")
        logger.info(f"📊 配置: 抓取间隔={CRAWL_INTERVAL}s, 处理间隔={PROCESS_INTERVAL}s")
        
        # 启动两个并发任务
        tasks = [
            asyncio.create_task(self.run_crawl_cycle()),
            asyncio.create_task(self.run_process_cycle())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"守护进程异常: {e}")
        finally:
            logger.info("👋 新闻抓取守护进程停止")

def signal_handler(signum, frame):
    """信号处理器"""
    global running
    logger.info(f"收到信号 {signum}，准备停止守护进程...")
    running = False

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        daemon = NewsCrawlerDaemon()
        asyncio.run(daemon.run())
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"守护进程运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
