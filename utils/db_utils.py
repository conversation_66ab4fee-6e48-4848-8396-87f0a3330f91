import os
import time
import logging
import pymysql
from dotenv import load_dotenv
from contextlib import contextmanager

load_dotenv(override=True)

# 配置日志
logger = logging.getLogger(__name__)

MYSQL_HOST = os.getenv("MYSQL_HOST")
MYSQL_PORT = int(os.getenv("MYSQL_PORT", 3306))
MYSQL_USER = os.getenv("MYSQL_USER")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD")
DATABASE_NAME = "cryptopanic"

# 连接池配置
CONNECTION_POOL_SIZE = 5
MAX_RETRIES = 3
RETRY_DELAY = 1

def get_db_connection(retry_count=0):
    """
    获取数据库连接，支持重试机制

    Args:
        retry_count: 当前重试次数

    Returns:
        数据库连接对象或 None
    """
    try:
        conn = pymysql.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=DATABASE_NAME,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=False,
            connect_timeout=10,
            read_timeout=30,
            write_timeout=30
        )
        return conn
    except pymysql.Error as e:
        logger.error(f"数据库连接失败 (尝试 {retry_count + 1}/{MAX_RETRIES}): {e}")

        if retry_count < MAX_RETRIES - 1:
            time.sleep(RETRY_DELAY * (retry_count + 1))  # 指数退避
            return get_db_connection(retry_count + 1)

        return None

@contextmanager
def get_db_cursor():
    """
    数据库连接上下文管理器
    """
    conn = get_db_connection()
    if conn is None:
        raise Exception("无法获取数据库连接")

    try:
        with conn.cursor() as cursor:
            yield cursor, conn
    except Exception as e:
        conn.rollback()
        raise e
    else:
        conn.commit()
    finally:
        conn.close()

def insert_news(news_item):
    """
    插入新闻到 cryptopanic_news 表

    Args:
        news_item: 字典，包含 title, published_at, source, url

    Returns:
        bool: 插入是否成功
    """
    try:
        with get_db_cursor() as (cursor, conn):
            # 适配现有数据库结构
            sql = """
                INSERT INTO cryptopanic_news (title, newsDatetime, url, negative, positive, important)
                VALUES (%s, %s, %s, 0, 0, 0)
                ON DUPLICATE KEY UPDATE
                    title=VALUES(title),
                    newsDatetime=VALUES(newsDatetime);
            """
            cursor.execute(sql, (
                news_item['title'][:512],  # 限制长度
                news_item['published_at'],
                news_item['url']
            ))

            if cursor.rowcount > 0:
                logger.info(f"新闻已插入或更新: {news_item['title'][:50]}...")

            return True

    except Exception as e:
        logger.error(f"插入新闻失败: {e}")
        return False

def get_unprocessed_news_urls(limit=50):
    """
    获取未处理的新闻 URL

    Args:
        limit: 限制返回数量

    Returns:
        list: 未处理新闻列表
    """
    try:
        with get_db_cursor() as (cursor, conn):
            sql = """
                SELECT id, url, title FROM news
                WHERE (full_content IS NULL OR summary IS NULL
                       OR full_content = '' OR summary = '')
                AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                ORDER BY created_at DESC
                LIMIT %s;
            """
            cursor.execute(sql, (limit,))
            result = cursor.fetchall()
            logger.info(f"查询到 {len(result)} 条未处理新闻")
            return result

    except Exception as e:
        logger.error(f"查询未处理新闻失败: {e}")
        return []

def update_news_content(news_id, full_content, summary):
    """
    更新新闻的内容和总结

    Args:
        news_id: 新闻ID
        full_content: 完整内容
        summary: 总结

    Returns:
        bool: 更新是否成功
    """
    try:
        with get_db_cursor() as (cursor, conn):
            sql = """
                UPDATE news
                SET full_content = %s, summary = %s
                WHERE id = %s;
            """
            cursor.execute(sql, (full_content, summary, news_id))

            if cursor.rowcount > 0:
                logger.info(f"新闻 ID {news_id} 内容已更新")
                return True
            else:
                logger.warning(f"新闻 ID {news_id} 未找到或未更新")
                return False

    except Exception as e:
        logger.error(f"更新新闻内容失败 ID={news_id}: {e}")
        return False

def get_recent_news(hours=24, coin=None, limit=50):
    """
    获取最近指定小时内的加密货币新闻

    Args:
        hours: 时间范围（小时）
        coin: 币种名称（可选）
        limit: 限制返回数量

    Returns:
        list: 新闻列表
    """
    try:
        with get_db_cursor() as (cursor, conn):
            # 适配现有数据库结构：cryptopanic_news表，newsDatetime字段
            sql = """
                SELECT cn.title, cn.url, cn.newsDatetime as published_at, s.name as source
                FROM cryptopanic_news cn
                LEFT JOIN source s ON cn.sourceId = s.id
                WHERE cn.newsDatetime >= NOW() - INTERVAL %s HOUR
            """
            params = [hours]

            if coin:
                # 扩展币种搜索，支持常见别名
                coin_patterns = [coin.lower()]

                # 添加常见币种别名
                coin_aliases = {
                    'btc': ['bitcoin', 'btc'],
                    'eth': ['ethereum', 'eth'],
                    'bnb': ['binance', 'bnb'],
                    'ada': ['cardano', 'ada'],
                    'sol': ['solana', 'sol'],
                    'dot': ['polkadot', 'dot'],
                    'matic': ['polygon', 'matic'],
                    'avax': ['avalanche', 'avax']
                }

                for key, aliases in coin_aliases.items():
                    if coin.lower() in aliases:
                        coin_patterns.extend(aliases)
                        break

                # 构建搜索条件
                coin_conditions = []
                for pattern in set(coin_patterns):
                    coin_conditions.append("cn.title LIKE %s")
                    params.append(f"%{pattern}%")

                sql += " AND (" + " OR ".join(coin_conditions) + ")"

            sql += " ORDER BY cn.newsDatetime DESC LIMIT %s;"
            params.append(limit)

            cursor.execute(sql, params)
            result = cursor.fetchall()

            # 为每条新闻添加简单的摘要（使用标题作为摘要）
            for news in result:
                news['summary'] = news['title']  # 使用标题作为摘要

            logger.info(f"查询到 {len(result)} 条最近新闻")
            return result

    except Exception as e:
        logger.error(f"查询最近新闻失败: {e}")
        return []

def get_bot_setting(key):
    """
    从 bot_settings 表获取指定 key 的值

    Args:
        key: 设置键名

    Returns:
        str: 设置值或 None
    """
    try:
        with get_db_cursor() as (cursor, conn):
            sql = "SELECT setting_value FROM bot_settings WHERE setting_key = %s;"
            cursor.execute(sql, (key,))
            result = cursor.fetchone()
            return result['setting_value'] if result else None

    except Exception as e:
        logger.error(f"获取 Bot 设置失败 key={key}: {e}")
        return None

def set_bot_setting(key, value):
    """
    设置或更新 bot_settings 表中指定 key 的值

    Args:
        key: 设置键名
        value: 设置值

    Returns:
        bool: 设置是否成功
    """
    try:
        with get_db_cursor() as (cursor, conn):
            sql = """
                INSERT INTO bot_settings (setting_key, setting_value)
                VALUES (%s, %s)
                ON DUPLICATE KEY UPDATE
                    setting_value=VALUES(setting_value),
                    last_updated=CURRENT_TIMESTAMP;
            """
            cursor.execute(sql, (key, value))
            logger.info(f"Bot 设置已更新: {key}")
            return True

    except Exception as e:
        logger.error(f"设置 Bot 设置失败 key={key}: {e}")
        return False
