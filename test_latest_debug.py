import asyncio
import sys
sys.path.append('/home/<USER>')

async def test_latest_debug():
    """调试/latest命令的问题"""
    print("🔍 调试 /latest 命令问题...")
    print("=" * 60)
    
    try:
        from telegram_bot.main import crypto_scraper
        
        # 测试1: 检查CryptoPanicScraper是否正常
        print("\n📡 测试1: CryptoPanicScraper基本功能")
        print(f"✅ CryptoPanicScraper实例: {crypto_scraper}")
        print(f"✅ 类型: {type(crypto_scraper)}")
        
        # 测试2: 测试scrape_latest_news方法
        print("\n📰 测试2: scrape_latest_news方法")
        try:
            news_data = await crypto_scraper.scrape_latest_news(limit=3)
            print(f"✅ 抓取结果: {len(news_data) if news_data else 0} 条新闻")
            
            if news_data:
                for i, news in enumerate(news_data[:2]):
                    print(f"   新闻 {i+1}: {news.get('title', '无标题')[:50]}...")
                    print(f"   来源: {news.get('source', '未知')}")
                    print(f"   时间: {news.get('time', '未知')}")
            else:
                print("⚠️  没有抓取到新闻数据")
                
        except Exception as e:
            print(f"❌ 抓取新闻失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试3: 测试AI总结功能
        print("\n🤖 测试3: AI总结功能")
        try:
            test_title = "Bitcoin price reaches new high"
            summary = await crypto_scraper.summarize_news(test_title)
            print(f"✅ 测试总结: {summary}")
        except Exception as e:
            print(f"❌ AI总结失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试4: 检查环境变量
        print("\n🔧 测试4: 检查环境变量")
        import os
        from dotenv import load_dotenv
        load_dotenv()
        
        gemini_key = os.getenv("GEMINI_API_KEY")
        print(f"✅ GEMINI_API_KEY: {'已设置' if gemini_key else '未设置'}")
        
        # 测试5: 模拟完整的latest命令流程
        print("\n🔄 测试5: 模拟完整流程")
        
        # 模拟欢迎检查
        print("1. 检查首次使用...")
        
        # 模拟抓取新闻
        print("2. 抓取最新新闻...")
        try:
            news_data = await crypto_scraper.scrape_latest_news(limit=5)
            if not news_data:
                print("❌ 无法获取新闻数据")
                return False
            print(f"✅ 获取到 {len(news_data)} 条新闻")
        except Exception as e:
            print(f"❌ 抓取失败: {e}")
            return False
        
        # 模拟处理新闻
        print("3. 处理新闻总结...")
        summaries = []
        for i, news in enumerate(news_data[:3]):  # 只处理前3条
            try:
                print(f"   处理第 {i+1} 条: {news['title'][:30]}...")
                
                # 跳过内容获取，直接生成总结
                summary = await crypto_scraper.summarize_news(news['title'])
                
                summaries.append({
                    'title': news['title'],
                    'summary': summary,
                    'source': news['source'],
                    'time': news['time']
                })
                print(f"   ✅ 完成")
                
            except Exception as e:
                print(f"   ❌ 失败: {e}")
        
        print(f"✅ 成功处理 {len(summaries)} 条新闻")
        
        # 模拟构建回复
        print("4. 构建回复消息...")
        if summaries:
            header = f"📊 **最新加密货币新闻快讯**\n\n🔍 **找到 {len(news_data)} 条最新新闻，已处理 {len(summaries)} 条**\n"
            print(f"✅ 标题消息长度: {len(header)} 字符")
            
            # 构建批次消息
            batch_size = 3
            batch_count = 0
            for batch_start in range(0, len(summaries), batch_size):
                batch_end = min(batch_start + batch_size, len(summaries))
                batch_summaries = summaries[batch_start:batch_end]
                
                batch_text = ""
                for i, item in enumerate(batch_summaries):
                    news_index = batch_start + i + 1
                    title_display = item['title'][:35] + "..." if len(item['title']) > 35 else item['title']
                    batch_text += f"**{news_index}. {title_display}**\n"
                    batch_text += f"{item['summary']}\n"
                    batch_text += f"📅 {item['source']} | {item['time']}\n\n"
                
                batch_count += 1
                print(f"   批次 {batch_count}: {len(batch_text)} 字符")
            
            print(f"✅ 生成 {batch_count} 个批次消息")
        
        print("\n🎉 /latest 命令流程测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_latest_debug())
    if success:
        print("\n✅ /latest 命令基本功能正常")
        print("💡 如果Telegram中仍有问题，可能是:")
        print("  • 网络连接问题")
        print("  • Webhook配置问题")
        print("  • Telegram API限制")
        print("  • 消息格式问题")
    else:
        print("\n❌ 发现问题，需要进一步修复")
