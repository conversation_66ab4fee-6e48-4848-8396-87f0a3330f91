import os
import pymysql
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True)

# 数据库连接信息
MYSQL_HOST = os.getenv("MYSQL_HOST")
MYSQL_PORT = int(os.getenv("MYSQL_PORT", 3306))
MYSQL_USER = os.getenv("MYSQL_USER")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD")
DATABASE_NAME = "nyxn_ai_db" # 数据库名称，已在规划中确认

def create_database_and_tables():
    conn = None
    cursor = None
    try:
        # 连接到 MySQL 服务器（不指定数据库）
        conn = pymysql.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD
        )
        cursor = conn.cursor()

        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DATABASE_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
        print(f"数据库 '{DATABASE_NAME}' 已创建或已存在。")

        # 切换到新创建的数据库
        cursor.execute(f"USE {DATABASE_NAME};")

        # 创建 news 表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS news (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(512) NOT NULL,
                published_at DATETIME NOT NULL,
                source VARCHAR(255) NOT NULL,
                url VARCHAR(1024) UNIQUE NOT NULL,
                full_content LONGTEXT,
                summary LONGTEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        """)
        print("表 'news' 已创建或已存在。")

        # 创建 bot_settings 表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bot_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) UNIQUE NOT NULL,
                setting_value LONGTEXT NOT NULL,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        """)
        print("表 'bot_settings' 已创建或已存在。")

        conn.commit()
        print("数据库和数据表初始化完成。")

    except pymysql.Error as e:
        print(f"数据库操作失败: {e}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    create_database_and_tables()
