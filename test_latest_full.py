import asyncio
import sys
sys.path.append('/home/<USER>')

async def test_latest_full():
    """完整测试/latest命令会显示多少条新闻"""
    print("🧪 完整测试 /latest 命令...")
    print("=" * 60)
    
    try:
        from telegram_bot.main import crypto_scraper
        
        # 1. 测试抓取阶段
        print("\n📡 阶段1: 新闻抓取")
        print("配置: limit=8")
        
        news_data = await crypto_scraper.scrape_latest_news(limit=8)
        print(f"✅ 抓取结果: {len(news_data)} 条新闻")
        
        if not news_data:
            print("❌ 没有抓取到新闻，/latest命令会失败")
            return False
        
        print(f"📊 抓取到的新闻列表:")
        for i, news in enumerate(news_data):
            print(f"  {i+1}. {news['title'][:60]}...")
            print(f"     来源: {news['source']} | 时间: {news['time']}")
        
        # 2. 测试处理阶段
        print(f"\n🔄 阶段2: 新闻处理 (处理全部 {len(news_data)} 条)")
        
        summaries = []
        for i, news in enumerate(news_data):
            try:
                print(f"正在处理第 {i+1}/{len(news_data)} 条: {news['title'][:40]}...")
                
                # 获取新闻内容（设置合理的超时时间）
                try:
                    content = await crypto_scraper.get_news_content(news['url'], timeout=15)
                    if content:
                        print(f"  ✅ 内容获取成功: {len(content)} 字符")
                    else:
                        print(f"  ⚠️  内容获取失败，将使用标题总结")
                except Exception as e:
                    print(f"  ⚠️  内容获取异常: {e}")
                    content = None

                # 生成总结
                summary = await crypto_scraper.summarize_news(news['title'], content)
                print(f"  ✅ AI总结完成: {len(summary)} 字符")

                summaries.append({
                    'title': news['title'],
                    'summary': summary,
                    'source': news['source'],
                    'time': news['time']
                })

            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
                # 即使失败也添加一个基本条目
                try:
                    summary = await crypto_scraper.summarize_news(news['title'])
                    summaries.append({
                        'title': news['title'],
                        'summary': summary,
                        'source': news['source'],
                        'time': news['time']
                    })
                    print(f"  ⚠️  使用基本总结")
                except Exception as e2:
                    print(f"  ❌ 基本总结也失败: {e2}")
                    summaries.append({
                        'title': news['title'],
                        'summary': f"📰 {news['title'][:60]}...",
                        'source': news['source'],
                        'time': news['time']
                    })
                    print(f"  ⚠️  使用标题作为总结")
        
        print(f"✅ 处理完成，成功处理 {len(summaries)} 条新闻")
        
        # 3. 测试发送阶段
        print(f"\n📱 阶段3: 消息发送模拟")
        
        if summaries:
            # 模拟标题消息
            header = f"📊 **最新加密货币新闻快讯**\n\n🔍 **找到 {len(news_data)} 条最新新闻**\n"
            print(f"标题消息: {len(header)} 字符")
            
            # 模拟分批发送，每批最多3条
            batch_size = 3
            batch_count = 0
            total_news_sent = 0
            
            for batch_start in range(0, len(summaries), batch_size):
                batch_end = min(batch_start + batch_size, len(summaries))
                batch_summaries = summaries[batch_start:batch_end]
                batch_count += 1
                
                print(f"\n批次 {batch_count}: 发送第 {batch_start+1}-{batch_end} 条新闻")
                
                batch_text = ""
                for i, item in enumerate(batch_summaries):
                    news_index = batch_start + i + 1
                    title_display = item['title'][:40] + "..." if len(item['title']) > 40 else item['title']
                    
                    batch_text += f"**{news_index}. {title_display}**\n"
                    batch_text += f"{item['summary']}\n"
                    batch_text += f"📅 {item['source']} | {item['time']}\n\n"
                    
                    total_news_sent += 1
                
                print(f"  批次内容长度: {len(batch_text)} 字符")
                print(f"  包含新闻: {len(batch_summaries)} 条")
            
            # 模拟时间戳
            from datetime import datetime
            footer = f"⏰ 更新时间: {datetime.now().strftime('%H:%M:%S')}"
            print(f"\n时间戳消息: {footer}")
            
            print(f"\n📊 发送总结:")
            print(f"  总批次数: {batch_count}")
            print(f"  总新闻数: {total_news_sent}")
            print(f"  预期显示: {total_news_sent} 条新闻")
            
        else:
            print("❌ 没有可发送的新闻")
        
        # 4. 验证配置
        print(f"\n⚙️  阶段4: 配置验证")
        print(f"✅ 抓取限制: 8 条")
        print(f"✅ 实际抓取: {len(news_data)} 条")
        print(f"✅ 成功处理: {len(summaries)} 条")
        print(f"✅ 批次大小: 3 条/批次")
        print(f"✅ 预期批次: {(len(summaries) + 2) // 3} 个批次")
        
        # 5. 结论
        print(f"\n🎯 结论:")
        if len(summaries) >= 6:
            print(f"✅ /latest 命令将显示 {len(summaries)} 条新闻，符合预期")
        elif len(summaries) >= 3:
            print(f"⚠️  /latest 命令将显示 {len(summaries)} 条新闻，少于预期但可接受")
        else:
            print(f"❌ /latest 命令将显示 {len(summaries)} 条新闻，明显少于预期")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_latest_full())
    if success:
        print("\n🎊 /latest 命令测试完成！")
        print("💡 如果显示的新闻数量少于预期，可能的原因:")
        print("  • 部分新闻处理失败")
        print("  • 网络超时导致内容获取失败")
        print("  • AI总结服务临时不可用")
        print("  • 可以通过增加错误处理来改善")
    else:
        print("\n❌ 测试失败，需要进一步调试")
