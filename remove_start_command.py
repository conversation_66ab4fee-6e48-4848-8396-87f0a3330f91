import asyncio
import sys
import os
sys.path.append('/home/<USER>')

async def remove_start_command():
    """为所有用户移除/start命令"""
    print("🔧 为所有用户移除/start命令...")
    print("=" * 60)
    
    try:
        # 1. 连接Bot
        from telegram_bot.main import TELEGRAM_BOT_TOKEN
        from telegram import Bot, BotCommand, BotCommandScopeDefault
        
        bot = Bot(token=TELEGRAM_BOT_TOKEN)
        
        # 2. 设置默认命令菜单（只在用户第一次使用时显示）
        print("\n📋 设置默认命令菜单:")
        default_commands = [
            BotCommand("news", "获取最近24小时加密货币新闻"),
            BotCommand("latest", "获取最新实时新闻快讯"),
            BotCommand("about", "了解系统功能和使用方法")
        ]
        
        await bot.set_my_commands(default_commands, scope=BotCommandScopeDefault())
        print("✅ 默认命令菜单已更新（移除/start）")
        
        # 验证设置
        updated_commands = await bot.get_my_commands()
        print(f"当前默认命令:")
        for cmd in updated_commands:
            print(f"  • /{cmd.command} - {cmd.description}")
        
        # 3. 为所有已知用户更新命令菜单
        print("\n👥 为所有已知用户更新命令菜单:")
        try:
            from utils.db_utils import get_all_users_from_db
            from telegram_bot.main import update_user_commands_for_activated_user
            
            all_users = get_all_users_from_db()
            print(f"数据库中共有 {len(all_users)} 个用户")
            
            success_count = 0
            for user_id in all_users:
                try:
                    await update_user_commands_for_activated_user(user_id, bot)
                    success_count += 1
                    print(f"  ✅ 用户 {user_id} 命令菜单已更新")
                except Exception as e:
                    print(f"  ⚠️  用户 {user_id} 更新失败: {e}")
            
            print(f"✅ 成功为 {success_count}/{len(all_users)} 个用户更新命令菜单")
            
        except Exception as e:
            print(f"❌ 批量更新失败: {e}")
        
        # 4. 清除所有可能的命令作用域
        print("\n🧹 清除可能的旧命令作用域:")
        
        # 这里我们不能清除所有作用域，因为我们不知道所有的chat_id
        # 但我们可以确保默认作用域是正确的
        
        print("✅ 默认作用域已更新")
        
        # 5. 提供用户指导
        print("\n💡 用户指导:")
        print("如果用户仍然看到/start命令，请告诉用户:")
        print("1. 完全关闭Telegram应用")
        print("2. 重新打开Telegram应用")
        print("3. 进入Bot对话")
        print("4. 查看命令菜单（应该只有news、latest、about）")
        print("5. 如果仍有问题，发送任何命令（如/news）激活更新")
        
        # 6. 创建持续监控脚本
        print("\n📝 创建持续监控脚本:")
        
        monitor_script = '''#!/bin/bash
# 持续监控和修复命令菜单

echo "🔄 持续监控命令菜单..."

cd /home/<USER>
source venv/bin/activate

# 每30秒检查一次
while true; do
    echo "$(date): 检查命令菜单..."
    
    python3 -c "
import asyncio
import sys
sys.path.append('/home/<USER>')

async def check_and_fix():
    from telegram_bot.main import TELEGRAM_BOT_TOKEN
    from telegram import Bot, BotCommand, BotCommandScopeDefault
    
    bot = Bot(token=TELEGRAM_BOT_TOKEN)
    
    # 确保默认命令菜单正确
    commands = [
        BotCommand('news', '获取最近24小时加密货币新闻'),
        BotCommand('latest', '获取最新实时新闻快讯'),
        BotCommand('about', '了解系统功能和使用方法')
    ]
    
    await bot.set_my_commands(commands, scope=BotCommandScopeDefault())
    print('命令菜单已确认更新')

try:
    asyncio.run(check_and_fix())
except:
    pass
"
    
    sleep 30
done
'''
        
        with open('/home/<USER>/monitor_commands.sh', 'w') as f:
            f.write(monitor_script)
        
        os.chmod('/home/<USER>/monitor_commands.sh', 0o755)
        print("✅ 监控脚本已创建: /home/<USER>/monitor_commands.sh")
        
        print("\n🎉 /start命令移除完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 移除失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(remove_start_command())
    if success:
        print("\n🎊 /start命令移除成功！")
        print("✨ 完成的操作：")
        print("  • 更新了默认命令菜单（移除/start）")
        print("  • 为所有已知用户更新了个性化菜单")
        print("  • 创建了监控脚本")
        print("\n💡 现在的状态：")
        print("  • 新用户将只看到news、latest、about命令")
        print("  • 老用户的命令菜单已更新")
        print("  • 用户需要重启Telegram应用查看新菜单")
        print("\n🔧 如果问题持续：")
        print("  • 运行监控脚本: ./monitor_commands.sh")
        print("  • 或手动修复: ./fix_command_menu.sh")
    else:
        print("\n❌ 移除失败，需要进一步调试")
