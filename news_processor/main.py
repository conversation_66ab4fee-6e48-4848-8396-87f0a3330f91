import os
import time
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from asyncio_throttle import Throttler
from utils.db_utils import get_unprocessed_news_urls, update_news_content
from langchain_google_genai import ChatGoogleGenerativeAI

# 加载环境变量
load_dotenv(override=True)

# 配置日志
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
logger = logging.getLogger(__name__)

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

# 初始化 Gemini 模型
llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash-exp",
    google_api_key=GEMINI_API_KEY,
    temperature=0.3
)

# 创建限流器，每秒最多处理2个请求
throttler = Throttler(rate_limit=2, period=1.0)

class WebContentExtractor:
    """网页内容提取器，使用 Playwright 获取网页内容"""

    def __init__(self):
        self.browser = None
        self.context = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=True,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.context = await self.browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    async def extract_content(self, url: str, timeout: int = 30000) -> str:
        """
        提取网页内容

        Args:
            url: 目标URL
            timeout: 超时时间（毫秒）

        Returns:
            提取的文本内容
        """
        try:
            async with throttler:
                page = await self.context.new_page()

                # 设置超时和拦截不必要的资源
                await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}",
                               lambda route: route.abort())

                # 导航到页面
                await page.goto(url, wait_until='domcontentloaded', timeout=timeout)

                # 等待页面加载完成
                await page.wait_for_load_state('networkidle', timeout=10000)

                # 获取页面内容
                content = await page.content()

                # 使用 BeautifulSoup 解析和清理内容
                soup = BeautifulSoup(content, 'lxml')

                # 移除脚本和样式标签
                for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
                    script.decompose()

                # 提取主要内容
                main_content = ""

                # 尝试找到主要内容区域
                content_selectors = [
                    'article', 'main', '[role="main"]',
                    '.content', '.post-content', '.article-content',
                    '.entry-content', '.post-body', '.story-body'
                ]

                for selector in content_selectors:
                    elements = soup.select(selector)
                    if elements:
                        main_content = ' '.join([elem.get_text(strip=True) for elem in elements])
                        break

                # 如果没有找到主要内容区域，使用整个body
                if not main_content:
                    body = soup.find('body')
                    if body:
                        main_content = body.get_text(strip=True)

                # 清理文本
                lines = main_content.split('\n')
                cleaned_lines = []
                for line in lines:
                    line = line.strip()
                    if line and len(line) > 10:  # 过滤掉太短的行
                        cleaned_lines.append(line)

                result = '\n'.join(cleaned_lines)

                await page.close()

                logger.info(f"成功提取内容，长度: {len(result)} 字符")
                return result

        except Exception as e:
            logger.error(f"提取网页内容失败 {url}: {str(e)}")
            return f"无法获取网页内容: {str(e)}"

async def summarize_content(content: str, title: str) -> str:
    """
    使用 Gemini 总结新闻内容

    Args:
        content: 新闻内容
        title: 新闻标题

    Returns:
        总结后的内容
    """
    try:
        # 限制内容长度，避免超过模型限制
        max_content_length = 8000
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."

        prompt = f"""请总结以下加密货币新闻内容，生成一个简洁的中文简讯。

要求：
1. 重点突出加密货币相关信息
2. 保持客观中性的语调
3. 简讯长度控制在200字以内
4. 如果内容与加密货币无关，请说明"此新闻与加密货币无直接关联"

新闻标题：{title}

新闻内容：
{content}

简讯总结："""

        response = await asyncio.to_thread(llm.invoke, prompt)
        summary = response.content.strip()

        logger.info(f"成功生成总结，长度: {len(summary)} 字符")
        return summary

    except Exception as e:
        logger.error(f"总结内容失败: {str(e)}")
        return f"总结生成失败: {str(e)}"

async def process_news_item(news_item, extractor: WebContentExtractor):
    """
    处理单个新闻项：获取内容并总结

    Args:
        news_item: 新闻项字典，包含id, url, title
        extractor: 网页内容提取器实例
    """
    news_id = news_item['id']
    url = news_item['url']
    title = news_item['title']

    logger.info(f"正在处理新闻: ID={news_id}, 标题={title}")
    logger.info(f"URL: {url}")

    try:
        # 使用 Playwright 获取网页内容
        full_content = await extractor.extract_content(url)

        if not full_content or len(full_content.strip()) < 50:
            logger.warning(f"获取到的内容太短或为空: {len(full_content)} 字符")
            full_content = f"无法获取有效内容，原始URL: {url}"

        logger.info(f"获取到内容长度: {len(full_content)} 字符")

        # 使用 Gemini 总结内容
        summary = await summarize_content(full_content, title)
        logger.info(f"生成总结: {summary[:100]}...")

        # 更新数据库
        success = update_news_content(news_id, full_content, summary)
        if success:
            logger.info(f"成功更新新闻 ID {news_id}")
        else:
            logger.error(f"更新数据库失败，新闻 ID {news_id}")

    except Exception as e:
        logger.error(f"处理新闻项失败 ID={news_id}: {str(e)}")
        # 即使失败也要记录，避免重复处理
        error_summary = f"处理失败: {str(e)}"
        update_news_content(news_id, f"处理失败: {str(e)}", error_summary)

async def main_loop():
    """主循环：定期处理未处理的新闻"""
    logger.info("新闻内容处理与总结服务启动...")

    # 安装 Playwright 浏览器（如果需要）
    try:
        import subprocess
        result = subprocess.run(['playwright', 'install', 'chromium'],
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            logger.info("Playwright 浏览器安装成功")
        else:
            logger.warning(f"Playwright 浏览器安装警告: {result.stderr}")
    except Exception as e:
        logger.warning(f"无法自动安装 Playwright 浏览器: {e}")

    retry_count = 0
    max_retries = 3

    while True:
        try:
            logger.info(f"[{datetime.now()}] 正在查询未处理新闻...")
            unprocessed_news = get_unprocessed_news_urls()

            if unprocessed_news:
                logger.info(f"发现 {len(unprocessed_news)} 条未处理新闻")

                # 使用异步上下文管理器
                async with WebContentExtractor() as extractor:
                    # 批量处理，避免同时处理太多
                    batch_size = 5
                    for i in range(0, len(unprocessed_news), batch_size):
                        batch = unprocessed_news[i:i + batch_size]
                        logger.info(f"处理批次 {i//batch_size + 1}: {len(batch)} 条新闻")

                        # 并发处理批次内的新闻
                        tasks = [process_news_item(news_item, extractor)
                                for news_item in batch]
                        await asyncio.gather(*tasks, return_exceptions=True)

                        # 批次间稍作休息
                        await asyncio.sleep(2)

                logger.info("本轮新闻处理完成")
            else:
                logger.info("没有未处理的新闻")

            # 重置重试计数
            retry_count = 0

        except Exception as e:
            retry_count += 1
            logger.error(f"主循环执行失败 (重试 {retry_count}/{max_retries}): {str(e)}")

            if retry_count >= max_retries:
                logger.error("达到最大重试次数，等待更长时间后继续")
                await asyncio.sleep(300)  # 等待5分钟
                retry_count = 0
            else:
                await asyncio.sleep(30)  # 等待30秒后重试
                continue

        # 每分钟运行一次
        await asyncio.sleep(60)

if __name__ == "__main__":
    try:
        asyncio.run(main_loop())
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"服务异常退出: {str(e)}")
        raise
