#!/bin/bash

# Telegram Bot 启动脚本
# 使用 screen 在后台运行

cd /home/<USER>

echo "🚀 启动 Telegram Bot..."

# 检查是否已有运行的Bot进程
if pgrep -f "python telegram_bot/main.py" > /dev/null; then
    echo "⚠️  检测到已运行的Bot进程，正在停止..."
    pkill -f "python telegram_bot/main.py"
    sleep 3
fi

# 检查是否已有tg-bot screen会话
if screen -list | grep -q "tg-bot"; then
    echo "⚠️  检测到已存在的screen会话，正在终止..."
    screen -S tg-bot -X quit
    sleep 2
fi

# 激活虚拟环境并启动Bot
echo "📱 在screen会话中启动Bot..."
screen -dmS tg-bot bash -c "
    cd /home/<USER>
    source venv/bin/activate
    export PYTHONPATH=/home/<USER>
    echo '🤖 Telegram Bot 正在启动...'
    python telegram_bot/main.py
"

# 等待一下让Bot启动
sleep 5

# 检查Bot是否成功启动
if pgrep -f "python telegram_bot/main.py" > /dev/null; then
    echo "✅ Bot启动成功！"
    
    # 检查健康状态
    echo "🔍 检查Bot健康状态..."
    if curl -s http://localhost:5000/health > /dev/null; then
        echo "✅ Bot健康检查通过"
        
        # 显示Bot信息
        echo "📊 Bot状态信息:"
        curl -s http://localhost:5000/health | python3 -m json.tool
    else
        echo "⚠️  Bot健康检查失败，可能还在启动中..."
    fi
    
    echo ""
    echo "📋 管理命令:"
    echo "  查看screen会话: screen -ls"
    echo "  连接到Bot会话: screen -r tg-bot"
    echo "  分离screen会话: Ctrl+A, D"
    echo "  停止Bot: screen -S tg-bot -X quit"
    echo ""
    echo "🤖 Bot命令:"
    echo "  /start - 开始使用Bot"
    echo "  /news - 获取最近24小时新闻"
    echo "  /latest - 获取最新实时新闻快讯"
    echo "  /about - 了解系统功能"
    echo ""
    echo "🌐 服务端点:"
    echo "  健康检查: http://localhost:5000/health"
    echo "  Webhook: https://hook.nyxn.ai/webhook"
    
else
    echo "❌ Bot启动失败！"
    echo "📋 故障排除:"
    echo "  查看screen会话: screen -r tg-bot"
    echo "  检查日志: tail -f /home/<USER>/bot.log"
    exit 1
fi
