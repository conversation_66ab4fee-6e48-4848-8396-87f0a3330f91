import asyncio
import sys
sys.path.append('/home/<USER>')

async def test_latest_command():
    """测试修复后的/latest命令"""
    print("🧪 测试修复后的 /latest 命令功能...")
    print("=" * 50)
    
    try:
        from telegram_bot.main import crypto_scraper
        
        # 1. 测试新闻抓取
        print("\n📡 步骤1: 测试新闻抓取...")
        news_data = await crypto_scraper.scrape_latest_news(limit=5)
        
        if not news_data:
            print("❌ 新闻抓取失败")
            return False
        
        print(f"✅ 成功抓取 {len(news_data)} 条新闻")
        
        # 显示抓取的新闻标题
        for i, news in enumerate(news_data):
            print(f"{i+1}. {news['title'][:50]}... ({news['time']})")
        
        # 2. 测试前2条新闻的处理
        print(f"\n🔄 步骤2: 测试前2条新闻的处理...")
        
        summaries = []
        for i, news in enumerate(news_data[:2]):
            print(f"\n处理第{i+1}条新闻: {news['title'][:30]}...")
            
            try:
                # 获取内容（短超时）
                content = await crypto_scraper.get_news_content(news['url'], timeout=10)
                if content:
                    print(f"   ✅ 内容获取成功: {len(content)} 字符")
                else:
                    print(f"   ⚠️  内容获取失败，将使用标题总结")
                
                # 生成总结
                summary = await crypto_scraper.summarize_news(news['title'], content)
                print(f"   ✅ 总结生成成功: {summary[:60]}...")
                
                summaries.append({
                    'title': news['title'],
                    'summary': summary,
                    'source': news['source'],
                    'time': news['time']
                })
                
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
                # 尝试基本总结
                try:
                    summary = await crypto_scraper.summarize_news(news['title'])
                    summaries.append({
                        'title': news['title'],
                        'summary': summary,
                        'source': news['source'],
                        'time': news['time']
                    })
                    print(f"   ✅ 基本总结成功")
                except Exception as e2:
                    print(f"   ❌ 基本总结也失败: {e2}")
        
        # 3. 构建最终回复
        print(f"\n📋 步骤3: 构建最终回复...")
        
        if summaries:
            response_text = f"📊 **最新加密货币新闻快讯**\\n\\n"
            response_text += f"🔍 **找到 {len(news_data)} 条最新新闻**\\n\\n"
            
            for i, item in enumerate(summaries):
                title_display = item['title'][:35] + "..." if len(item['title']) > 35 else item['title']
                response_text += f"**{i+1}. {title_display}**\\n"
                response_text += f"{item['summary']}\\n"
                response_text += f"📅 {item['source']} | {item['time']}\\n\\n"
            
            response_text += f"⏰ 更新时间: 10:20:00"
            
            print("✅ 回复消息构建成功")
            print(f"📏 消息长度: {len(response_text)} 字符")
            
            if len(response_text) > 4096:
                print("⚠️  消息过长，将分段发送")
            else:
                print("✅ 消息长度适中，可以单条发送")
            
            # 显示回复预览
            print("\\n📄 回复预览:")
            print("-" * 40)
            print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
            print("-" * 40)
            
        else:
            print("❌ 无法生成总结")
            return False
        
        print("\\n🎉 /latest 命令测试完成！")
        print("✅ 新闻抓取: 正常")
        print("✅ 内容获取: 正常")
        print("✅ AI总结: 正常")
        print("✅ 消息构建: 正常")
        print("✅ 错误处理: 正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_latest_command())
    if success:
        print("\\n🎊 /latest 命令修复成功！现在可以在Telegram中正常使用了！")
    else:
        print("\\n❌ 测试失败，需要进一步调试")
