import asyncio
import sys
sys.path.append('/home/<USER>')

from telegram_bot.main import CryptoPanicScraper
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_integrated_scraper():
    """测试集成的CryptoPanicScraper功能"""
    print("🧪 测试集成的CryptoPanicScraper功能...")
    print("=" * 60)
    
    # 创建抓取器实例
    scraper = CryptoPanicScraper()
    
    # 1. 测试新闻抓取
    print("\n📡 步骤1: 测试新闻抓取...")
    news_data = await scraper.scrape_latest_news(limit=5)
    
    if not news_data:
        print("❌ 新闻抓取失败")
        return
    
    print(f"✅ 成功抓取 {len(news_data)} 条新闻")
    
    # 显示抓取的新闻
    for i, news in enumerate(news_data):
        print(f"{i+1}. {news['title'][:50]}...")
        print(f"   来源: {news['source']} | 时间: {news['time']}")
        print(f"   链接: {news['url']}")
    
    # 2. 测试内容获取
    print(f"\n📄 步骤2: 测试内容获取...")
    test_news = news_data[0]  # 测试第一条新闻
    
    print(f"正在获取内容: {test_news['title'][:40]}...")
    content = await scraper.get_news_content(test_news['url'])
    
    if content:
        print(f"✅ 内容获取成功，长度: {len(content)} 字符")
        print(f"内容预览: {content[:200]}...")
    else:
        print("⚠️  内容获取失败或为空")
    
    # 3. 测试AI总结
    print(f"\n🤖 步骤3: 测试AI总结...")
    
    # 测试有内容的总结
    summary_with_content = await scraper.summarize_news(test_news['title'], content)
    print(f"✅ 有内容的总结: {summary_with_content}")
    
    # 测试仅标题的总结
    summary_title_only = await scraper.summarize_news(test_news['title'])
    print(f"✅ 仅标题的总结: {summary_title_only}")
    
    # 4. 测试完整流程（模拟/latest命令）
    print(f"\n🔄 步骤4: 测试完整流程（模拟/latest命令）...")
    
    print("模拟Telegram Bot /latest命令的处理流程:")
    print(f"📊 找到 {len(news_data)} 条最新新闻")
    
    summaries = []
    for i, news in enumerate(news_data[:3]):
        print(f"\n处理第{i+1}条新闻: {news['title'][:30]}...")
        
        # 获取内容
        content = await scraper.get_news_content(news['url'])
        
        # 生成总结
        summary = await scraper.summarize_news(news['title'], content)
        
        summaries.append({
            'title': news['title'],
            'summary': summary,
            'source': news['source'],
            'time': news['time']
        })
        
        print(f"✅ 第{i+1}条处理完成")
    
    # 5. 构建最终回复格式
    print(f"\n📋 步骤5: 构建最终回复格式...")
    print("=" * 60)
    
    response_text = f"📊 **最新加密货币新闻快讯**\n\n"
    response_text += f"🔍 **找到 {len(news_data)} 条最新新闻**\n\n"
    
    for i, item in enumerate(summaries):
        title_display = item['title'][:40] + "..." if len(item['title']) > 40 else item['title']
        response_text += f"**{i+1}. {title_display}**\n"
        response_text += f"{item['summary']}\n"
        response_text += f"📅 {item['source']} | {item['time']}\n\n"
    
    response_text += f"⏰ 更新时间: 09:30:00"
    
    print(response_text)
    print("=" * 60)
    
    # 6. 测试数据库保存功能
    print(f"\n💾 步骤6: 测试数据库保存功能...")
    
    try:
        from utils.db_utils import insert_news
        
        saved_count = 0
        for news_item in news_data:
            try:
                if insert_news(news_item):
                    saved_count += 1
            except Exception as e:
                print(f"保存失败: {e}")
        
        print(f"✅ 成功保存 {saved_count}/{len(news_data)} 条新闻到数据库")
        
    except Exception as e:
        print(f"❌ 数据库保存测试失败: {e}")
    
    print(f"\n🎉 所有测试完成！")
    print("✅ CryptoPanicScraper功能已成功集成到Telegram Bot")
    print("✅ 可以使用 /latest 命令获取最新新闻快讯")
    print("✅ 可以使用 /scrape 命令手动抓取新闻到数据库")

if __name__ == "__main__":
    asyncio.run(test_integrated_scraper())
