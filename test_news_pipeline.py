import asyncio
import sys
sys.path.append('/home/<USER>')

from playwright.async_api import async_playwright
from datetime import datetime
import logging
import google.generativeai as genai
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    model = genai.GenerativeModel('gemini-2.0-flash')

async def test_news_scraping():
    """测试新闻抓取功能"""
    logger.info("🧪 开始测试新闻抓取功能...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            # 1. 访问Cryptopanic主页
            logger.info("📡 访问 Cryptopanic 主页...")
            await page.goto('https://cryptopanic.com/')
            await page.wait_for_load_state('networkidle')
            
            # 2. 获取最新新闻列表
            logger.info("🔍 查找最新新闻...")
            news_items = await page.query_selector_all('.news-row')
            logger.info(f"✅ 找到 {len(news_items)} 条新闻")
            
            if not news_items:
                logger.error("❌ 未找到新闻项目")
                await browser.close()
                return False
            
            # 3. 测试获取前3条新闻的详细信息
            news_data = []
            for i, item in enumerate(news_items[:3]):
                try:
                    logger.info(f"\n📰 处理第 {i+1} 条新闻...")
                    
                    # 获取新闻链接
                    link_elem = await item.query_selector('a[href*="/news/"]')
                    if not link_elem:
                        logger.warning(f"   ⚠️  第{i+1}条新闻未找到链接")
                        continue
                    
                    title = await link_elem.inner_text()
                    href = await link_elem.get_attribute('href')
                    full_url = href if href.startswith('http') else f"https://cryptopanic.com{href}"
                    
                    logger.info(f"   标题: {title[:60]}...")
                    logger.info(f"   链接: {full_url}")
                    
                    # 获取时间信息
                    time_elem = await item.query_selector('.time, .timestamp')
                    time_text = "刚刚"
                    if time_elem:
                        time_text = await time_elem.inner_text()
                    logger.info(f"   时间: {time_text}")
                    
                    # 获取来源
                    source_elem = await item.query_selector('.source, .domain')
                    source = "CryptoPanic"
                    if source_elem:
                        source_text = await source_elem.inner_text()
                        if source_text.strip():
                            source = source_text.strip()
                    logger.info(f"   来源: {source}")
                    
                    news_data.append({
                        'title': title.strip(),
                        'url': full_url,
                        'source': source,
                        'time': time_text,
                        'published_at': datetime.now()
                    })
                    
                except Exception as e:
                    logger.error(f"   ❌ 处理第{i+1}条新闻失败: {e}")
            
            await browser.close()
            
            if not news_data:
                logger.error("❌ 未获取到有效新闻数据")
                return False
            
            logger.info(f"✅ 成功获取 {len(news_data)} 条新闻基本信息")
            
            # 4. 测试获取新闻详细内容
            await test_content_extraction(news_data[0])  # 测试第一条新闻
            
            # 5. 测试AI总结功能
            if GEMINI_API_KEY:
                await test_ai_summarization(news_data[0])
            else:
                logger.warning("⚠️  未配置GEMINI_API_KEY，跳过AI总结测试")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 新闻抓取测试失败: {e}")
            await browser.close()
            return False

async def test_content_extraction(news_item):
    """测试新闻内容提取"""
    logger.info(f"\n🔍 测试内容提取: {news_item['title'][:40]}...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            # 访问新闻详情页
            await page.goto(news_item['url'], timeout=30000)
            await page.wait_for_load_state('networkidle')
            
            # 尝试多种内容选择器
            content_selectors = [
                'article',
                '.content',
                '.post-content',
                '.news-content',
                '.article-body',
                'main',
                '.entry-content'
            ]
            
            content = ""
            for selector in content_selectors:
                elements = await page.query_selector_all(selector)
                if elements:
                    for elem in elements:
                        text = await elem.inner_text()
                        if len(text) > 100:  # 只要有意义的内容
                            content += text + "\n"
                    if content:
                        logger.info(f"   ✅ 使用选择器 '{selector}' 找到内容")
                        break
            
            if not content:
                # 如果没找到特定内容区域，获取整个body
                body_text = await page.inner_text('body')
                if len(body_text) > 200:
                    content = body_text
                    logger.info("   ✅ 使用body内容")
            
            if content:
                # 清理内容
                lines = content.split('\n')
                cleaned_lines = []
                for line in lines:
                    line = line.strip()
                    if line and len(line) > 10:
                        cleaned_lines.append(line)
                
                final_content = '\n'.join(cleaned_lines[:20])  # 限制行数
                
                logger.info(f"   ✅ 成功提取内容，长度: {len(final_content)} 字符")
                logger.info(f"   内容预览: {final_content[:200]}...")
                
                news_item['content'] = final_content
                return True
            else:
                logger.warning("   ⚠️  未能提取到有效内容")
                return False
                
        except Exception as e:
            logger.error(f"   ❌ 内容提取失败: {e}")
            return False
        finally:
            await browser.close()

async def test_ai_summarization(news_item):
    """测试AI总结功能"""
    logger.info(f"\n🤖 测试AI总结功能...")
    
    if 'content' not in news_item:
        logger.warning("   ⚠️  没有内容可供总结")
        return False
    
    try:
        content = news_item['content']
        title = news_item['title']
        
        # 限制内容长度
        max_length = 3000
        if len(content) > max_length:
            content = content[:max_length] + "..."
        
        prompt = f"""请总结以下加密货币新闻内容，生成一个简洁的中文简讯。

要求：
1. 重点突出加密货币相关信息
2. 保持客观中性的语调
3. 简讯长度控制在200字以内
4. 如果内容与加密货币无关，请说明"此新闻与加密货币无直接关联"

新闻标题：{title}

新闻内容：
{content}

简讯总结："""

        logger.info("   🔄 正在调用Gemini API...")
        response = model.generate_content(prompt)
        summary = response.text.strip()
        
        logger.info(f"   ✅ AI总结成功，长度: {len(summary)} 字符")
        logger.info(f"   总结内容: {summary}")
        
        news_item['summary'] = summary
        return True
        
    except Exception as e:
        logger.error(f"   ❌ AI总结失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始新闻抓取和处理流程测试")
    logger.info("=" * 60)
    
    success = await test_news_scraping()
    
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("🎉 测试完成！新闻抓取和处理流程正常工作")
        logger.info("✅ 可以获取最新快讯")
        logger.info("✅ 可以提取新闻内容") 
        logger.info("✅ 可以进行AI总结")
    else:
        logger.error("❌ 测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    asyncio.run(main())
