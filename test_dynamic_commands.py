import asyncio
import sys
import requests
import os
sys.path.append('/home/<USER>')

async def test_dynamic_commands():
    """测试动态命令菜单功能"""
    print("🧪 测试动态命令菜单功能...")
    print("=" * 60)
    
    try:
        # 1. 检查Bot健康状态
        print("\n🤖 检查Bot健康状态:")
        try:
            response = requests.get("http://localhost:5000/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 状态: {data['status']}")
                print(f"✅ 用户名: {data['bot_username']}")
                print(f"✅ Webhook: {'已配置' if data['webhook_configured'] else '未配置'}")
            else:
                print(f"❌ HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False
        
        # 2. 测试动态命令菜单更新函数
        print("\n🔧 测试动态命令菜单更新函数:")
        from telegram_bot.main import update_user_commands_for_activated_user
        
        # 创建模拟的Bot对象
        class MockBot:
            def __init__(self):
                self.commands_set = {}
            
            async def set_my_commands(self, commands, scope=None):
                scope_key = scope.chat_id if scope else "default"
                self.commands_set[scope_key] = [cmd.command for cmd in commands]
                print(f"     为 {scope_key} 设置命令: {[cmd.command for cmd in commands]}")
                return True
        
        mock_bot = MockBot()
        test_user_id = 12345
        
        print(f"   测试为用户 {test_user_id} 更新命令菜单:")
        
        try:
            await update_user_commands_for_activated_user(test_user_id, mock_bot)
            
            if test_user_id in mock_bot.commands_set:
                commands = mock_bot.commands_set[test_user_id]
                print(f"     设置的命令: {commands}")
                
                if 'start' not in commands:
                    print("     ✅ /start命令已移除")
                else:
                    print("     ❌ /start命令仍然存在")
                
                expected_commands = ['news', 'latest', 'about']
                if all(cmd in commands for cmd in expected_commands):
                    print("     ✅ 其他命令正常保留")
                else:
                    print("     ❌ 其他命令缺失")
            else:
                print("     ❌ 命令未设置")
                
        except Exception as e:
            print(f"     ❌ 更新命令菜单失败: {e}")
        
        # 3. 测试用户激活流程
        print("\n🔄 测试用户激活流程:")
        from telegram_bot.main import ensure_user_activated, welcomed_users, load_user_data
        
        # 保存原始状态
        original_users = welcomed_users.copy()
        original_data = load_user_data()
        
        # 测试新用户激活
        test_user_id_2 = 54321
        test_chat_id = 98765
        
        print(f"   测试新用户 {test_user_id_2} 激活:")
        
        # 确保是新用户
        if test_user_id_2 in welcomed_users:
            welcomed_users.remove(test_user_id_2)
        
        user_data = load_user_data()
        if test_user_id_2 in user_data:
            del user_data[test_user_id_2]
            from telegram_bot.main import save_user_data
            save_user_data(user_data)
        
        print(f"     激活前状态: {test_user_id_2 in welcomed_users}")
        
        # 激活用户
        was_new = ensure_user_activated(test_user_id_2, test_chat_id, "test_user", "Test User")
        
        print(f"     激活后状态: {test_user_id_2 in welcomed_users}")
        print(f"     是否新用户: {was_new}")
        
        if was_new:
            print("     ✅ 新用户激活成功，应该触发命令菜单更新")
        else:
            print("     ❌ 新用户激活失败")
        
        # 4. 测试/start命令的命令菜单更新
        print("\n👋 测试/start命令的命令菜单更新:")
        
        # 创建模拟的Update对象
        class MockUser:
            def __init__(self, user_id, username="test", first_name="Test"):
                self.id = user_id
                self.username = username
                self.first_name = first_name
        
        class MockChat:
            def __init__(self, chat_id):
                self.id = chat_id
        
        class MockMessage:
            def __init__(self):
                self.reply_count = 0
                self.last_message = ""
            
            async def reply_text(self, text, parse_mode=None):
                self.reply_count += 1
                self.last_message = text
                return True
        
        class MockUpdate:
            def __init__(self, user_id, chat_id, username="test", first_name="Test"):
                self.effective_user = MockUser(user_id, username, first_name)
                self.effective_chat = MockChat(chat_id)
                self.message = MockMessage()
                self.bot = MockBot()
            
            def get_bot(self):
                return self.bot
        
        # 测试新用户/start
        new_user_id = 99999
        new_chat_id = 88888
        
        print(f"   测试新用户 {new_user_id} 的/start命令:")
        
        # 确保是新用户
        if new_user_id in welcomed_users:
            welcomed_users.remove(new_user_id)
        user_data = load_user_data()
        if new_user_id in user_data:
            del user_data[new_user_id]
            from telegram_bot.main import save_user_data
            save_user_data(user_data)
        
        # 模拟/start命令逻辑
        user_data = load_user_data()
        is_new_user = new_user_id not in user_data
        
        print(f"     数据库检查: {'新用户' if is_new_user else '老用户'}")
        
        if is_new_user:
            # 模拟注册用户
            from telegram_bot.main import register_user
            register_user(new_user_id, new_chat_id, "new_user", "New User")
            
            # 模拟命令菜单更新
            mock_update = MockUpdate(new_user_id, new_chat_id)
            await update_user_commands_for_activated_user(new_user_id, mock_update.get_bot())
            
            if new_user_id in mock_update.bot.commands_set:
                commands = mock_update.bot.commands_set[new_user_id]
                print(f"     /start后命令菜单: {commands}")
                
                if 'start' not in commands:
                    print("     ✅ /start命令已从菜单中移除")
                else:
                    print("     ❌ /start命令仍在菜单中")
            else:
                print("     ❌ 命令菜单未更新")
        
        # 5. 测试其他命令的菜单更新
        print("\n📱 测试其他命令的菜单更新:")
        
        commands_to_test = [
            {'name': '/news', 'user_id': 11111, 'chat_id': 22222},
            {'name': '/latest', 'user_id': 33333, 'chat_id': 44444},
            {'name': '/about', 'user_id': 55555, 'chat_id': 66666}
        ]
        
        for cmd in commands_to_test:
            print(f"\n   测试 {cmd['name']} 命令的菜单更新:")
            user_id = cmd['user_id']
            chat_id = cmd['chat_id']
            
            # 确保是新用户
            if user_id in welcomed_users:
                welcomed_users.remove(user_id)
            user_data = load_user_data()
            if user_id in user_data:
                del user_data[user_id]
                from telegram_bot.main import save_user_data
                save_user_data(user_data)
            
            print(f"     用户ID: {user_id}")
            print(f"     激活前状态: {user_id in welcomed_users}")
            
            # 模拟命令激活
            was_new = ensure_user_activated(user_id, chat_id, f"user_{user_id}", f"User{user_id}")
            
            if was_new:
                # 模拟命令菜单更新
                mock_update = MockUpdate(user_id, chat_id)
                await update_user_commands_for_activated_user(user_id, mock_update.get_bot())
                
                if user_id in mock_update.bot.commands_set:
                    commands = mock_update.bot.commands_set[user_id]
                    print(f"     激活后命令菜单: {commands}")
                    
                    if 'start' not in commands:
                        print(f"     ✅ {cmd['name']} 激活后/start命令已移除")
                    else:
                        print(f"     ❌ {cmd['name']} 激活后/start命令仍存在")
                else:
                    print(f"     ❌ {cmd['name']} 激活后命令菜单未更新")
            else:
                print(f"     ❌ {cmd['name']} 用户激活失败")
        
        # 6. 验证修复效果
        print("\n🎯 验证修复效果:")
        print("✅ 动态命令菜单更新函数: update_user_commands_for_activated_user()")
        print("✅ /start命令后立即更新菜单: 移除/start命令")
        print("✅ 其他命令激活后更新菜单: 移除/start命令")
        print("✅ 用户特定命令作用域: BotCommandScopeChat")
        print("✅ 老用户命令菜单: 只显示news、latest、about")
        
        # 恢复原始状态
        welcomed_users.clear()
        welcomed_users.update(original_users)
        from telegram_bot.main import save_user_data
        save_user_data(original_data)
        
        print("\n🎉 动态命令菜单测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_dynamic_commands())
    if success:
        print("\n🎊 动态命令菜单修复成功！")
        print("✨ 主要改进：")
        print("  • 用户激活后立即更新命令菜单")
        print("  • /start命令后自动移除/start选项")
        print("  • 其他命令激活用户后也移除/start")
        print("  • 使用用户特定的命令作用域")
        print("  • 老用户只看到实用的命令")
        print("\n💡 现在的用户体验:")
        print("  • 新用户点击/start后，命令菜单中不再有/start")
        print("  • 通过任何命令激活的用户都会更新菜单")
        print("  • 老用户只看到news、latest、about三个命令")
        print("  • 命令菜单根据用户状态动态调整")
    else:
        print("\n❌ 测试失败，需要进一步调试")
