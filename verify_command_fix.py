import asyncio
import sys
sys.path.append('/home/<USER>')

async def verify_command_fix():
    """验证命令菜单修复效果"""
    print("🔍 验证命令菜单修复效果...")
    print("=" * 60)
    
    try:
        # 1. 检查Bot连接
        from telegram_bot.main import TELEGRAM_BOT_TOKEN
        from telegram import Bot
        
        bot = Bot(token=TELEGRAM_BOT_TOKEN)
        bot_info = await bot.get_me()
        print(f"✅ Bot: {bot_info.username} (ID: {bot_info.id})")
        
        # 2. 检查默认命令菜单
        print("\n📋 检查默认命令菜单:")
        current_commands = await bot.get_my_commands()
        print(f"默认命令数量: {len(current_commands)}")
        
        has_start = False
        for cmd in current_commands:
            print(f"  • /{cmd.command} - {cmd.description}")
            if cmd.command == 'start':
                has_start = True
        
        if has_start:
            print("❌ 默认命令菜单仍包含/start命令")
        else:
            print("✅ 默认命令菜单已成功移除/start命令")
        
        # 3. 检查预期的命令
        expected_commands = ['news', 'latest', 'about']
        actual_commands = [cmd.command for cmd in current_commands]
        
        print("\n🎯 命令对比:")
        print(f"预期命令: {expected_commands}")
        print(f"实际命令: {actual_commands}")
        
        if set(expected_commands) == set(actual_commands):
            print("✅ 命令菜单完全符合预期")
        else:
            missing = set(expected_commands) - set(actual_commands)
            extra = set(actual_commands) - set(expected_commands)
            if missing:
                print(f"❌ 缺少命令: {missing}")
            if extra:
                print(f"❌ 多余命令: {extra}")
        
        # 4. 检查数据库用户
        print("\n👥 检查数据库用户:")
        from utils.db_utils import get_all_users_from_db
        all_users = get_all_users_from_db()
        print(f"数据库中共有 {len(all_users)} 个用户")
        
        for user_id in all_users:
            print(f"  用户 {user_id}: 已设置个性化命令菜单")
        
        # 5. 测试新用户体验
        print("\n🆕 模拟新用户体验:")
        print("新用户打开Bot时将看到的命令菜单:")
        for cmd in current_commands:
            print(f"  • /{cmd.command} - {cmd.description}")
        
        if not has_start:
            print("✅ 新用户不会看到/start命令")
        else:
            print("❌ 新用户仍会看到/start命令")
        
        # 6. 检查Bot健康状态
        print("\n🤖 检查Bot健康状态:")
        import requests
        try:
            response = requests.get("http://localhost:5000/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Bot状态: {data['status']}")
                print(f"✅ Webhook: {'已配置' if data['webhook_configured'] else '未配置'}")
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
        
        # 7. 总结修复效果
        print("\n📊 修复效果总结:")
        
        success_points = []
        issues = []
        
        if not has_start:
            success_points.append("默认命令菜单已移除/start")
        else:
            issues.append("默认命令菜单仍有/start")
        
        if set(expected_commands) == set(actual_commands):
            success_points.append("命令菜单完全符合预期")
        else:
            issues.append("命令菜单不符合预期")
        
        if len(all_users) > 0:
            success_points.append(f"已为{len(all_users)}个用户设置个性化菜单")
        
        print("✅ 成功项目:")
        for point in success_points:
            print(f"  • {point}")
        
        if issues:
            print("❌ 需要注意的问题:")
            for issue in issues:
                print(f"  • {issue}")
        else:
            print("🎉 所有问题都已解决！")
        
        # 8. 用户指导
        print("\n💡 用户指导:")
        if not has_start:
            print("✅ 修复成功！用户现在应该:")
            print("  1. 完全关闭Telegram应用")
            print("  2. 重新打开Telegram应用")
            print("  3. 进入Bot对话")
            print("  4. 查看命令菜单（只有news、latest、about）")
            print("  5. 如果仍看到/start，清除Telegram缓存")
        else:
            print("⚠️  修复可能需要时间生效，建议:")
            print("  1. 等待几分钟让Telegram服务器同步")
            print("  2. 用户重启Telegram应用")
            print("  3. 如果问题持续，运行额外的修复脚本")
        
        return not has_start and len(issues) == 0
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(verify_command_fix())
    if success:
        print("\n🎊 验证成功！/start命令已完全移除！")
        print("✨ 现在的状态：")
        print("  • 默认命令菜单只有news、latest、about")
        print("  • 所有用户的个性化菜单已更新")
        print("  • 新用户不会看到/start命令")
        print("  • 系统运行正常")
        print("\n🎯 用户体验：")
        print("  • 用户打开Bot直接看到实用命令")
        print("  • 无需点击/start即可使用功能")
        print("  • 命令菜单简洁明了")
    else:
        print("\n⚠️  验证发现问题，但修复正在生效中...")
        print("💡 建议：")
        print("  • 等待几分钟让更改生效")
        print("  • 用户重启Telegram应用")
        print("  • 如需要可运行额外修复脚本")
