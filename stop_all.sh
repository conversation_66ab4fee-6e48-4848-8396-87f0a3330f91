#!/bin/bash

# 一键停止全部服务脚本

echo "🛑 停止 nyxn-ai 新闻系统全部服务..."
echo "=============================================="

cd /home/<USER>

# 1. 停止Telegram Bot
echo ""
echo "🤖 停止Telegram Bot..."
./stop_bot.sh

# 2. 停止新闻抓取守护进程
echo ""
echo "📡 停止新闻抓取守护进程..."
./stop_news_crawler.sh

# 3. 强制清理残留进程
echo ""
echo "🧹 清理残留进程..."

# 清理Telegram Bot进程
if pgrep -f "python telegram_bot/main.py" > /dev/null; then
    echo "🔄 强制停止Telegram Bot进程..."
    pkill -f "python telegram_bot/main.py"
    sleep 2
fi

# 清理新闻抓取进程
if pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    echo "🔄 强制停止新闻抓取进程..."
    pkill -f "python.*news_crawler_daemon.py"
    sleep 2
fi

# 清理相关screen会话
if screen -list | grep -q "tg-bot"; then
    echo "📱 清理tg-bot screen会话..."
    screen -S tg-bot -X quit
fi

if screen -list | grep -q "news-crawler"; then
    echo "📱 清理news-crawler screen会话..."
    screen -S news-crawler -X quit
fi

# 4. 验证停止状态
echo ""
echo "🔍 验证停止状态..."
echo "=============================================="

if ! pgrep -f "python telegram_bot/main.py" > /dev/null; then
    echo "✅ Telegram Bot: 已停止"
else
    echo "❌ Telegram Bot: 仍在运行"
fi

if ! pgrep -f "python.*news_crawler_daemon.py" > /dev/null; then
    echo "✅ 新闻抓取守护进程: 已停止"
else
    echo "❌ 新闻抓取守护进程: 仍在运行"
fi

# 检查端口
if ! netstat -tlnp 2>/dev/null | grep ":5000" > /dev/null; then
    echo "✅ 端口5000: 已释放"
else
    echo "⚠️  端口5000: 仍被占用"
fi

echo ""
echo "🎉 全部服务停止完成！"
echo ""
echo "📋 管理命令:"
echo "  启动全部服务: ./start_all.sh"
echo "  查看全状态: ./status_all.sh"
echo "  重启全部服务: ./restart_all.sh"
