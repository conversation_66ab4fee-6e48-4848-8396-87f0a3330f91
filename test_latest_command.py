import asyncio
import sys
sys.path.append('/home/<USER>')

from playwright.async_api import async_playwright
import google.generativeai as genai
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    model = genai.GenerativeModel('gemini-pro')

async def test_latest_functionality():
    """测试最新新闻功能"""
    print("🧪 测试最新新闻功能...")
    
    # 1. 抓取最新新闻
    print("\n📡 步骤1: 抓取最新新闻...")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto('https://cryptopanic.com/')
            await page.wait_for_load_state('networkidle')
            
            news_items = await page.query_selector_all('.news-row')
            print(f"✅ 找到 {len(news_items)} 条新闻")
            
            news_data = []
            for i, item in enumerate(news_items[:3]):  # 只处理前3条
                try:
                    link_elem = await item.query_selector('a[href*="/news/"]')
                    if not link_elem:
                        continue
                        
                    title = await link_elem.inner_text()
                    href = await link_elem.get_attribute('href')
                    
                    if not title or not href:
                        continue
                        
                    full_url = href if href.startswith('http') else f"https://cryptopanic.com{href}"
                    
                    time_elem = await item.query_selector('.time, .timestamp')
                    time_text = "刚刚"
                    if time_elem:
                        time_text = await time_elem.inner_text()
                    
                    source_elem = await item.query_selector('.source, .domain')
                    source = "CryptoPanic"
                    if source_elem:
                        source_text = await source_elem.inner_text()
                        if source_text.strip():
                            source = source_text.strip()
                    
                    news_data.append({
                        'title': title.strip(),
                        'url': full_url,
                        'source': source,
                        'time': time_text
                    })
                    
                    print(f"   {i+1}. {title[:50]}... ({time_text})")
                    
                except Exception as e:
                    print(f"   ❌ 解析第{i+1}条新闻失败: {e}")
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ 抓取失败: {e}")
            await browser.close()
            return
    
    if not news_data:
        print("❌ 未获取到新闻数据")
        return
    
    # 2. 测试内容获取和总结
    print(f"\n🔍 步骤2: 获取前2条新闻的详细内容和总结...")
    
    for i, news in enumerate(news_data[:2]):
        print(f"\n处理第{i+1}条新闻: {news['title'][:40]}...")
        
        # 获取内容
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                await page.goto(news['url'], timeout=30000)
                await page.wait_for_load_state('networkidle')
                
                content = await page.inner_text('body')
                
                lines = content.split('\n')
                cleaned_lines = []
                for line in lines:
                    line = line.strip()
                    if line and len(line) > 10:
                        cleaned_lines.append(line)
                
                final_content = '\n'.join(cleaned_lines[:30])
                
                await browser.close()
                
                if final_content and len(final_content) > 100:
                    print(f"   ✅ 获取内容成功，长度: {len(final_content)} 字符")
                    
                    # AI总结
                    if GEMINI_API_KEY:
                        try:
                            prompt = f"""请总结以下加密货币新闻内容，生成一个简洁的中文简讯。

要求：
1. 重点突出加密货币相关信息
2. 保持客观中性的语调
3. 简讯长度控制在150字以内

新闻标题：{news['title']}

新闻内容：
{final_content[:2000]}

简讯总结："""

                            response = model.generate_content(prompt)
                            summary = response.text.strip()
                            print(f"   ✅ AI总结成功:")
                            print(f"   📝 {summary}")
                            
                        except Exception as e:
                            print(f"   ❌ AI总结失败: {e}")
                    else:
                        print("   ⚠️  未配置GEMINI_API_KEY")
                else:
                    print(f"   ❌ 内容获取失败或内容太短")
                    
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
                await browser.close()
    
    print("\n🎉 测试完成！")
    print("\n📋 功能总结:")
    print(f"✅ 可以抓取到 {len(news_data)} 条最新新闻")
    print("✅ 可以获取新闻详细内容")
    print("✅ 可以进行AI总结" if GEMINI_API_KEY else "⚠️  需要配置GEMINI_API_KEY进行AI总结")
    print("\n🤖 现在可以在Telegram中使用 /latest 命令测试！")

if __name__ == "__main__":
    asyncio.run(test_latest_functionality())
